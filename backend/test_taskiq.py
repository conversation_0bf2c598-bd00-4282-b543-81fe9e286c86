#!/usr/bin/env python3
"""
测试Taskiq任务处理
Test Taskiq Task Processing
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from loguru import logger
from backend.tasks import broker
from backend.tasks.conversation_tasks import process_conversation_message


async def test_direct_task():
    """直接测试任务函数"""
    logger.info("Testing direct task execution...")
    
    try:
        result = await process_conversation_message(
            conversation_id="test_conversation",
            message_id="test_message",
            content="Test message",
            message_type="user",
            context={},
            user_id=None
        )
        
        logger.success(f"Direct task execution successful: {result}")
        return True
    except Exception as e:
        logger.error(f"Direct task execution failed: {e}")
        return False


async def test_taskiq_submission():
    """测试Taskiq任务提交"""
    logger.info("Testing taskiq task submission...")
    
    try:
        # 启动broker
        await broker.startup()
        
        # 提交任务
        task = await process_conversation_message.kiq(
            conversation_id="test_conversation",
            message_id="test_message",
            content="Test taskiq message",
            message_type="user",
            context={},
            user_id=None
        )
        
        logger.info(f"Task submitted with ID: {task.task_id}")
        
        # 等待结果
        result = await task.wait_result(timeout=30)
        logger.success(f"Taskiq task execution successful: {result}")
        
        await broker.shutdown()
        return True
    except Exception as e:
        logger.error(f"Taskiq task execution failed: {e}")
        await broker.shutdown()
        return False


async def main():
    """主函数"""
    logger.info("Starting Taskiq tests...")
    
    # 测试1: 直接执行任务函数
    logger.info("=" * 50)
    logger.info("Test 1: Direct task execution")
    direct_success = await test_direct_task()
    
    # 测试2: 通过Taskiq提交任务
    logger.info("=" * 50)
    logger.info("Test 2: Taskiq task submission")
    taskiq_success = await test_taskiq_submission()
    
    # 结果总结
    logger.info("=" * 50)
    logger.info("Test Results:")
    logger.info(f"Direct execution: {'✅ PASS' if direct_success else '❌ FAIL'}")
    logger.info(f"Taskiq submission: {'✅ PASS' if taskiq_success else '❌ FAIL'}")
    
    if direct_success and taskiq_success:
        logger.success("All tests passed!")
        return 0
    else:
        logger.error("Some tests failed!")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
