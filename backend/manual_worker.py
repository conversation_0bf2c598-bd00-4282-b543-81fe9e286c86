#!/usr/bin/env python3
"""
手动Worker - 直接从Redis队列处理任务
Manual Worker - Process tasks directly from Redis queue
"""

import asyncio
import json
import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import redis
from loguru import logger
from backend.config import get_config
from backend.tasks.conversation_tasks import process_conversation_message
from backend.tasks.animation_tasks import generate_animation_task


async def process_conversation_task(task_data: dict):
    """处理对话任务"""
    try:
        kwargs = task_data.get("kwargs", {})
        
        logger.info(f"Processing conversation task: {task_data['task_id']}")
        
        result = await process_conversation_message(
            conversation_id=kwargs.get("conversation_id"),
            message_id=kwargs.get("message_id"),
            content=kwargs.get("content"),
            message_type=kwargs.get("message_type"),
            context=kwargs.get("context"),
            user_id=kwargs.get("user_id"),
        )
        
        logger.success(f"Conversation task completed: {task_data['task_id']}")
        return result
        
    except Exception as e:
        logger.error(f"Conversation task failed: {task_data['task_id']}, error: {e}")
        raise


async def process_animation_task(task_data: dict):
    """处理动画任务"""
    try:
        kwargs = task_data.get("kwargs", {})
        
        logger.info(f"Processing animation task: {task_data['task_id']}")
        
        result = await generate_animation_task(
            conversation_id=kwargs.get("conversation_id"),
            message_id=kwargs.get("message_id"),
            text=kwargs.get("text"),
            character_id=kwargs.get("character_id"),
            context=kwargs.get("context"),
            user_id=kwargs.get("user_id"),
        )
        
        logger.success(f"Animation task completed: {task_data['task_id']}")
        return result
        
    except Exception as e:
        logger.error(f"Animation task failed: {task_data['task_id']}, error: {e}")
        raise


async def worker_loop():
    """Worker主循环"""
    config = get_config()
    
    # 连接Redis
    redis_client = redis.Redis(
        host=config.redis.redis_host,
        port=config.redis.redis_port,
        db=config.redis.redis_db,
        decode_responses=True
    )
    
    logger.info(f"Connected to Redis: {config.redis.redis_host}:{config.redis.redis_port}")
    
    while True:
        try:
            # 从conversation队列获取任务
            task_json = redis_client.lpop("conversation")
            if task_json:
                task_data = json.loads(task_json)
                task_name = task_data.get("task_name", "")
                
                if "conversation_tasks:process_conversation_message" in task_name:
                    await process_conversation_task(task_data)
                elif "animation_tasks:generate_animation_task" in task_name:
                    await process_animation_task(task_data)
                else:
                    logger.warning(f"Unknown task type: {task_name}")
            
            # 从animation队列获取任务
            task_json = redis_client.lpop("animation")
            if task_json:
                task_data = json.loads(task_json)
                task_name = task_data.get("task_name", "")
                
                if "animation_tasks:generate_animation_task" in task_name:
                    await process_animation_task(task_data)
                else:
                    logger.warning(f"Unknown task type: {task_name}")
            
            # 如果没有任务，短暂休息
            if not task_json:
                await asyncio.sleep(1)
                
        except KeyboardInterrupt:
            logger.info("Worker stopped by user")
            break
        except Exception as e:
            logger.error(f"Worker error: {e}")
            await asyncio.sleep(5)  # 错误后等待5秒再继续


async def main():
    """主函数"""
    logger.info("Starting Manual Worker...")

    try:
        # 初始化数据库和Beanie
        from backend.database import init_database
        await init_database()
        logger.info("Database and Beanie initialized")

        await worker_loop()
    except Exception as e:
        logger.error(f"Worker failed: {e}")
        return 1

    logger.info("Worker stopped")
    return 0


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
