2025-06-10 17:54:26 | INFO     | backend.config:validate_config:305 - Created directory: ./output/animations
2025-06-10 17:54:26 | INFO     | backend.config:validate_config:305 - Created directory: ./temp/animation_data
2025-06-10 17:54:26 | SUCCESS  | backend.config:validate_config:326 - Configuration validation passed
2025-06-10 17:54:26 | INFO     | backend.app:startup_event:109 - Starting Motion Agent API server with Taskiq...
2025-06-10 17:54:26 | INFO     | backend.app:startup_event:112 - Initializing MongoDB database...
2025-06-10 17:54:26 | INFO     | backend.database:init_database:52 - Initializing MongoDB database...
2025-06-10 17:54:26 | SUCCESS  | backend.database:init_database:83 - MongoDB database initialized successfully: motion_agent
2025-06-10 17:54:26 | INFO     | backend.app:startup_event:119 - Creating database indexes...
2025-06-10 17:54:26 | INFO     | backend.database:create_indexes:261 - Creating database indexes...
2025-06-10 17:54:26 | SUCCESS  | backend.database:create_indexes:281 - Database indexes created successfully
2025-06-10 17:54:26 | INFO     | backend.app:startup_event:123 - Initializing taskiq...
2025-06-10 17:54:26 | INFO     | backend.tasks:init_taskiq:89 - Initializing taskiq...
2025-06-10 17:54:26 | SUCCESS  | backend.tasks:init_taskiq:93 - Taskiq initialized successfully
2025-06-10 17:54:26 | SUCCESS  | backend.app:startup_event:133 - Motion Agent API with Taskiq initialized successfully
2025-06-10 17:54:26 | INFO     | backend.app:startup_event:134 - Motion Agent API is ready to serve requests
2025-06-10 17:54:45 | INFO     | backend.app:shutdown_event:140 - Shutting down Motion Agent API...
2025-06-10 17:54:45 | INFO     | backend.tasks:shutdown_taskiq:104 - Shutting down taskiq...
2025-06-10 17:54:45 | SUCCESS  | backend.tasks:shutdown_taskiq:106 - Taskiq shutdown successfully
2025-06-10 17:54:45 | INFO     | backend.database:close_database:96 - Closing MongoDB connections...
2025-06-10 17:54:45 | SUCCESS  | backend.database:close_database:103 - MongoDB connections closed
2025-06-10 17:54:45 | SUCCESS  | backend.app:shutdown_event:148 - Motion Agent API shutdown completed
2025-06-10 17:54:45 | SUCCESS  | backend.config:validate_config:326 - Configuration validation passed
2025-06-10 17:54:45 | INFO     | backend.app:startup_event:109 - Starting Motion Agent API server with Taskiq...
2025-06-10 17:54:45 | INFO     | backend.app:startup_event:112 - Initializing MongoDB database...
2025-06-10 17:54:45 | INFO     | backend.database:init_database:52 - Initializing MongoDB database...
2025-06-10 17:54:45 | SUCCESS  | backend.database:init_database:83 - MongoDB database initialized successfully: motion_agent
2025-06-10 17:54:45 | INFO     | backend.app:startup_event:119 - Creating database indexes...
2025-06-10 17:54:45 | INFO     | backend.database:create_indexes:261 - Creating database indexes...
2025-06-10 17:54:45 | SUCCESS  | backend.database:create_indexes:281 - Database indexes created successfully
2025-06-10 17:54:45 | INFO     | backend.app:startup_event:123 - Initializing taskiq...
2025-06-10 17:54:45 | INFO     | backend.tasks:init_taskiq:89 - Initializing taskiq...
2025-06-10 17:54:45 | SUCCESS  | backend.tasks:init_taskiq:93 - Taskiq initialized successfully
2025-06-10 17:54:45 | SUCCESS  | backend.app:startup_event:133 - Motion Agent API with Taskiq initialized successfully
2025-06-10 17:54:45 | INFO     | backend.app:startup_event:134 - Motion Agent API is ready to serve requests
2025-06-10 17:54:54 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 17:54:58 | INFO     | backend.app:shutdown_event:140 - Shutting down Motion Agent API...
2025-06-10 17:54:58 | INFO     | backend.tasks:shutdown_taskiq:104 - Shutting down taskiq...
2025-06-10 17:54:58 | SUCCESS  | backend.tasks:shutdown_taskiq:106 - Taskiq shutdown successfully
2025-06-10 17:54:58 | INFO     | backend.database:close_database:96 - Closing MongoDB connections...
2025-06-10 17:54:58 | SUCCESS  | backend.database:close_database:103 - MongoDB connections closed
2025-06-10 17:54:58 | SUCCESS  | backend.app:shutdown_event:148 - Motion Agent API shutdown completed
2025-06-10 17:54:59 | SUCCESS  | backend.config:validate_config:326 - Configuration validation passed
2025-06-10 17:54:59 | INFO     | backend.app:startup_event:109 - Starting Motion Agent API server with Taskiq...
2025-06-10 17:54:59 | INFO     | backend.app:startup_event:112 - Initializing MongoDB database...
2025-06-10 17:54:59 | INFO     | backend.database:init_database:52 - Initializing MongoDB database...
2025-06-10 17:54:59 | SUCCESS  | backend.database:init_database:83 - MongoDB database initialized successfully: motion_agent
2025-06-10 17:54:59 | INFO     | backend.app:startup_event:119 - Creating database indexes...
2025-06-10 17:54:59 | INFO     | backend.database:create_indexes:261 - Creating database indexes...
2025-06-10 17:54:59 | SUCCESS  | backend.database:create_indexes:281 - Database indexes created successfully
2025-06-10 17:54:59 | INFO     | backend.app:startup_event:123 - Initializing taskiq...
2025-06-10 17:54:59 | INFO     | backend.tasks:init_taskiq:89 - Initializing taskiq...
2025-06-10 17:54:59 | SUCCESS  | backend.tasks:init_taskiq:93 - Taskiq initialized successfully
2025-06-10 17:54:59 | SUCCESS  | backend.app:startup_event:133 - Motion Agent API with Taskiq initialized successfully
2025-06-10 17:54:59 | INFO     | backend.app:startup_event:134 - Motion Agent API is ready to serve requests
2025-06-10 17:55:11 | INFO     | backend.app:shutdown_event:140 - Shutting down Motion Agent API...
2025-06-10 17:55:11 | INFO     | backend.tasks:shutdown_taskiq:104 - Shutting down taskiq...
2025-06-10 17:55:11 | SUCCESS  | backend.tasks:shutdown_taskiq:106 - Taskiq shutdown successfully
2025-06-10 17:55:11 | INFO     | backend.database:close_database:96 - Closing MongoDB connections...
2025-06-10 17:55:11 | SUCCESS  | backend.database:close_database:103 - MongoDB connections closed
2025-06-10 17:55:11 | SUCCESS  | backend.app:shutdown_event:148 - Motion Agent API shutdown completed
2025-06-10 17:55:12 | SUCCESS  | backend.config:validate_config:326 - Configuration validation passed
2025-06-10 17:55:12 | INFO     | backend.app:startup_event:109 - Starting Motion Agent API server with Taskiq...
2025-06-10 17:55:12 | INFO     | backend.app:startup_event:112 - Initializing MongoDB database...
2025-06-10 17:55:12 | INFO     | backend.database:init_database:52 - Initializing MongoDB database...
2025-06-10 17:55:12 | SUCCESS  | backend.database:init_database:83 - MongoDB database initialized successfully: motion_agent
2025-06-10 17:55:12 | INFO     | backend.app:startup_event:119 - Creating database indexes...
2025-06-10 17:55:12 | INFO     | backend.database:create_indexes:261 - Creating database indexes...
2025-06-10 17:55:12 | SUCCESS  | backend.database:create_indexes:281 - Database indexes created successfully
2025-06-10 17:55:12 | INFO     | backend.app:startup_event:123 - Initializing taskiq...
2025-06-10 17:55:12 | INFO     | backend.tasks:init_taskiq:89 - Initializing taskiq...
2025-06-10 17:55:12 | SUCCESS  | backend.tasks:init_taskiq:93 - Taskiq initialized successfully
2025-06-10 17:55:12 | SUCCESS  | backend.app:startup_event:133 - Motion Agent API with Taskiq initialized successfully
2025-06-10 17:55:12 | INFO     | backend.app:startup_event:134 - Motion Agent API is ready to serve requests
2025-06-10 17:55:21 | INFO     | backend.app:shutdown_event:140 - Shutting down Motion Agent API...
2025-06-10 17:55:21 | INFO     | backend.tasks:shutdown_taskiq:104 - Shutting down taskiq...
2025-06-10 17:55:21 | SUCCESS  | backend.tasks:shutdown_taskiq:106 - Taskiq shutdown successfully
2025-06-10 17:55:21 | INFO     | backend.database:close_database:96 - Closing MongoDB connections...
2025-06-10 17:55:21 | SUCCESS  | backend.database:close_database:103 - MongoDB connections closed
2025-06-10 17:55:21 | SUCCESS  | backend.app:shutdown_event:148 - Motion Agent API shutdown completed
2025-06-10 17:55:22 | SUCCESS  | backend.config:validate_config:326 - Configuration validation passed
2025-06-10 17:55:22 | INFO     | backend.app:startup_event:109 - Starting Motion Agent API server with Taskiq...
2025-06-10 17:55:22 | INFO     | backend.app:startup_event:112 - Initializing MongoDB database...
2025-06-10 17:55:22 | INFO     | backend.database:init_database:52 - Initializing MongoDB database...
2025-06-10 17:55:22 | SUCCESS  | backend.database:init_database:83 - MongoDB database initialized successfully: motion_agent
2025-06-10 17:55:22 | INFO     | backend.app:startup_event:119 - Creating database indexes...
2025-06-10 17:55:22 | INFO     | backend.database:create_indexes:261 - Creating database indexes...
2025-06-10 17:55:22 | SUCCESS  | backend.database:create_indexes:281 - Database indexes created successfully
2025-06-10 17:55:22 | INFO     | backend.app:startup_event:123 - Initializing taskiq...
2025-06-10 17:55:22 | INFO     | backend.tasks:init_taskiq:89 - Initializing taskiq...
2025-06-10 17:55:22 | SUCCESS  | backend.tasks:init_taskiq:93 - Taskiq initialized successfully
2025-06-10 17:55:22 | SUCCESS  | backend.app:startup_event:133 - Motion Agent API with Taskiq initialized successfully
2025-06-10 17:55:22 | INFO     | backend.app:startup_event:134 - Motion Agent API is ready to serve requests
2025-06-10 17:55:24 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 17:55:44 | INFO     | backend.app:shutdown_event:140 - Shutting down Motion Agent API...
2025-06-10 17:55:44 | INFO     | backend.tasks:shutdown_taskiq:104 - Shutting down taskiq...
2025-06-10 17:55:44 | SUCCESS  | backend.tasks:shutdown_taskiq:106 - Taskiq shutdown successfully
2025-06-10 17:55:44 | INFO     | backend.database:close_database:96 - Closing MongoDB connections...
2025-06-10 17:55:44 | SUCCESS  | backend.database:close_database:103 - MongoDB connections closed
2025-06-10 17:55:44 | SUCCESS  | backend.app:shutdown_event:148 - Motion Agent API shutdown completed
2025-06-10 17:55:45 | SUCCESS  | backend.config:validate_config:326 - Configuration validation passed
2025-06-10 17:55:45 | INFO     | backend.app:startup_event:109 - Starting Motion Agent API server with Taskiq...
2025-06-10 17:55:45 | INFO     | backend.app:startup_event:112 - Initializing MongoDB database...
2025-06-10 17:55:45 | INFO     | backend.database:init_database:52 - Initializing MongoDB database...
2025-06-10 17:55:45 | SUCCESS  | backend.database:init_database:83 - MongoDB database initialized successfully: motion_agent
2025-06-10 17:55:45 | INFO     | backend.app:startup_event:119 - Creating database indexes...
2025-06-10 17:55:45 | INFO     | backend.database:create_indexes:261 - Creating database indexes...
2025-06-10 17:55:45 | SUCCESS  | backend.database:create_indexes:281 - Database indexes created successfully
2025-06-10 17:55:45 | INFO     | backend.app:startup_event:123 - Initializing taskiq...
2025-06-10 17:55:45 | INFO     | backend.tasks:init_taskiq:89 - Initializing taskiq...
2025-06-10 17:55:45 | SUCCESS  | backend.tasks:init_taskiq:93 - Taskiq initialized successfully
2025-06-10 17:55:45 | SUCCESS  | backend.app:startup_event:133 - Motion Agent API with Taskiq initialized successfully
2025-06-10 17:55:45 | INFO     | backend.app:startup_event:134 - Motion Agent API is ready to serve requests
2025-06-10 17:55:54 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 17:55:56 | INFO     | backend.app:shutdown_event:140 - Shutting down Motion Agent API...
2025-06-10 17:55:56 | INFO     | backend.tasks:shutdown_taskiq:104 - Shutting down taskiq...
2025-06-10 17:55:56 | SUCCESS  | backend.tasks:shutdown_taskiq:106 - Taskiq shutdown successfully
2025-06-10 17:55:56 | INFO     | backend.database:close_database:96 - Closing MongoDB connections...
2025-06-10 17:55:56 | SUCCESS  | backend.database:close_database:103 - MongoDB connections closed
2025-06-10 17:55:56 | SUCCESS  | backend.app:shutdown_event:148 - Motion Agent API shutdown completed
2025-06-10 17:55:56 | SUCCESS  | backend.config:validate_config:326 - Configuration validation passed
2025-06-10 17:55:56 | INFO     | backend.app:startup_event:109 - Starting Motion Agent API server with Taskiq...
2025-06-10 17:55:56 | INFO     | backend.app:startup_event:112 - Initializing MongoDB database...
2025-06-10 17:55:56 | INFO     | backend.database:init_database:52 - Initializing MongoDB database...
2025-06-10 17:55:56 | SUCCESS  | backend.database:init_database:83 - MongoDB database initialized successfully: motion_agent
2025-06-10 17:55:56 | INFO     | backend.app:startup_event:119 - Creating database indexes...
2025-06-10 17:55:56 | INFO     | backend.database:create_indexes:261 - Creating database indexes...
2025-06-10 17:55:56 | SUCCESS  | backend.database:create_indexes:281 - Database indexes created successfully
2025-06-10 17:55:56 | INFO     | backend.app:startup_event:123 - Initializing taskiq...
2025-06-10 17:55:56 | INFO     | backend.tasks:init_taskiq:89 - Initializing taskiq...
2025-06-10 17:55:56 | SUCCESS  | backend.tasks:init_taskiq:93 - Taskiq initialized successfully
2025-06-10 17:55:56 | SUCCESS  | backend.app:startup_event:133 - Motion Agent API with Taskiq initialized successfully
2025-06-10 17:55:56 | INFO     | backend.app:startup_event:134 - Motion Agent API is ready to serve requests
2025-06-10 17:56:07 | INFO     | backend.app:shutdown_event:140 - Shutting down Motion Agent API...
2025-06-10 17:56:07 | INFO     | backend.tasks:shutdown_taskiq:104 - Shutting down taskiq...
2025-06-10 17:56:07 | SUCCESS  | backend.tasks:shutdown_taskiq:106 - Taskiq shutdown successfully
2025-06-10 17:56:07 | INFO     | backend.database:close_database:96 - Closing MongoDB connections...
2025-06-10 17:56:07 | SUCCESS  | backend.database:close_database:103 - MongoDB connections closed
2025-06-10 17:56:07 | SUCCESS  | backend.app:shutdown_event:148 - Motion Agent API shutdown completed
2025-06-10 17:56:07 | SUCCESS  | backend.config:validate_config:326 - Configuration validation passed
2025-06-10 17:56:07 | INFO     | backend.app:startup_event:109 - Starting Motion Agent API server with Taskiq...
2025-06-10 17:56:07 | INFO     | backend.app:startup_event:112 - Initializing MongoDB database...
2025-06-10 17:56:07 | INFO     | backend.database:init_database:52 - Initializing MongoDB database...
2025-06-10 17:56:07 | SUCCESS  | backend.database:init_database:83 - MongoDB database initialized successfully: motion_agent
2025-06-10 17:56:07 | INFO     | backend.app:startup_event:119 - Creating database indexes...
2025-06-10 17:56:07 | INFO     | backend.database:create_indexes:261 - Creating database indexes...
2025-06-10 17:56:07 | SUCCESS  | backend.database:create_indexes:281 - Database indexes created successfully
2025-06-10 17:56:07 | INFO     | backend.app:startup_event:123 - Initializing taskiq...
2025-06-10 17:56:07 | INFO     | backend.tasks:init_taskiq:89 - Initializing taskiq...
2025-06-10 17:56:07 | SUCCESS  | backend.tasks:init_taskiq:93 - Taskiq initialized successfully
2025-06-10 17:56:07 | SUCCESS  | backend.app:startup_event:133 - Motion Agent API with Taskiq initialized successfully
2025-06-10 17:56:07 | INFO     | backend.app:startup_event:134 - Motion Agent API is ready to serve requests
2025-06-10 17:56:18 | INFO     | backend.app:shutdown_event:140 - Shutting down Motion Agent API...
2025-06-10 17:56:18 | INFO     | backend.tasks:shutdown_taskiq:104 - Shutting down taskiq...
2025-06-10 17:56:18 | SUCCESS  | backend.tasks:shutdown_taskiq:106 - Taskiq shutdown successfully
2025-06-10 17:56:18 | INFO     | backend.database:close_database:96 - Closing MongoDB connections...
2025-06-10 17:56:18 | SUCCESS  | backend.database:close_database:103 - MongoDB connections closed
2025-06-10 17:56:18 | SUCCESS  | backend.app:shutdown_event:148 - Motion Agent API shutdown completed
2025-06-10 17:56:18 | SUCCESS  | backend.config:validate_config:326 - Configuration validation passed
2025-06-10 17:56:18 | INFO     | backend.app:startup_event:109 - Starting Motion Agent API server with Taskiq...
2025-06-10 17:56:18 | INFO     | backend.app:startup_event:112 - Initializing MongoDB database...
2025-06-10 17:56:18 | INFO     | backend.database:init_database:52 - Initializing MongoDB database...
2025-06-10 17:56:18 | SUCCESS  | backend.database:init_database:83 - MongoDB database initialized successfully: motion_agent
2025-06-10 17:56:18 | INFO     | backend.app:startup_event:119 - Creating database indexes...
2025-06-10 17:56:18 | INFO     | backend.database:create_indexes:261 - Creating database indexes...
2025-06-10 17:56:18 | SUCCESS  | backend.database:create_indexes:281 - Database indexes created successfully
2025-06-10 17:56:18 | INFO     | backend.app:startup_event:123 - Initializing taskiq...
2025-06-10 17:56:18 | INFO     | backend.tasks:init_taskiq:89 - Initializing taskiq...
2025-06-10 17:56:18 | SUCCESS  | backend.tasks:init_taskiq:93 - Taskiq initialized successfully
2025-06-10 17:56:18 | SUCCESS  | backend.app:startup_event:133 - Motion Agent API with Taskiq initialized successfully
2025-06-10 17:56:18 | INFO     | backend.app:startup_event:134 - Motion Agent API is ready to serve requests
2025-06-10 17:56:24 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 17:56:48 | INFO     | backend.app:shutdown_event:140 - Shutting down Motion Agent API...
2025-06-10 17:56:48 | INFO     | backend.tasks:shutdown_taskiq:104 - Shutting down taskiq...
2025-06-10 17:56:48 | SUCCESS  | backend.tasks:shutdown_taskiq:106 - Taskiq shutdown successfully
2025-06-10 17:56:48 | INFO     | backend.database:close_database:96 - Closing MongoDB connections...
2025-06-10 17:56:48 | SUCCESS  | backend.database:close_database:103 - MongoDB connections closed
2025-06-10 17:56:48 | SUCCESS  | backend.app:shutdown_event:148 - Motion Agent API shutdown completed
2025-06-10 17:56:49 | SUCCESS  | backend.config:validate_config:326 - Configuration validation passed
2025-06-10 17:56:49 | INFO     | backend.app:startup_event:109 - Starting Motion Agent API server with Taskiq...
2025-06-10 17:56:49 | INFO     | backend.app:startup_event:112 - Initializing MongoDB database...
2025-06-10 17:56:49 | INFO     | backend.database:init_database:52 - Initializing MongoDB database...
2025-06-10 17:56:49 | SUCCESS  | backend.database:init_database:83 - MongoDB database initialized successfully: motion_agent
2025-06-10 17:56:49 | INFO     | backend.app:startup_event:119 - Creating database indexes...
2025-06-10 17:56:49 | INFO     | backend.database:create_indexes:261 - Creating database indexes...
2025-06-10 17:56:49 | SUCCESS  | backend.database:create_indexes:281 - Database indexes created successfully
2025-06-10 17:56:49 | INFO     | backend.app:startup_event:123 - Initializing taskiq...
2025-06-10 17:56:49 | INFO     | backend.tasks:init_taskiq:89 - Initializing taskiq...
2025-06-10 17:56:49 | SUCCESS  | backend.tasks:init_taskiq:93 - Taskiq initialized successfully
2025-06-10 17:56:49 | SUCCESS  | backend.app:startup_event:133 - Motion Agent API with Taskiq initialized successfully
2025-06-10 17:56:49 | INFO     | backend.app:startup_event:134 - Motion Agent API is ready to serve requests
2025-06-10 17:56:54 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 17:57:51 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 17:58:51 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 17:58:55 | INFO     | backend.app:shutdown_event:140 - Shutting down Motion Agent API...
2025-06-10 17:58:55 | INFO     | backend.tasks:shutdown_taskiq:104 - Shutting down taskiq...
2025-06-10 17:58:55 | SUCCESS  | backend.tasks:shutdown_taskiq:106 - Taskiq shutdown successfully
2025-06-10 17:58:55 | INFO     | backend.database:close_database:96 - Closing MongoDB connections...
2025-06-10 17:58:55 | SUCCESS  | backend.database:close_database:103 - MongoDB connections closed
2025-06-10 17:58:55 | SUCCESS  | backend.app:shutdown_event:148 - Motion Agent API shutdown completed
2025-06-10 17:58:56 | SUCCESS  | backend.config:validate_config:326 - Configuration validation passed
2025-06-10 17:58:56 | INFO     | backend.app:startup_event:109 - Starting Motion Agent API server with Taskiq...
2025-06-10 17:58:56 | INFO     | backend.app:startup_event:112 - Initializing MongoDB database...
2025-06-10 17:58:56 | INFO     | backend.database:init_database:52 - Initializing MongoDB database...
2025-06-10 17:58:56 | SUCCESS  | backend.database:init_database:83 - MongoDB database initialized successfully: motion_agent
2025-06-10 17:58:56 | INFO     | backend.app:startup_event:119 - Creating database indexes...
2025-06-10 17:58:56 | INFO     | backend.database:create_indexes:261 - Creating database indexes...
2025-06-10 17:58:56 | SUCCESS  | backend.database:create_indexes:281 - Database indexes created successfully
2025-06-10 17:58:56 | INFO     | backend.app:startup_event:123 - Initializing taskiq...
2025-06-10 17:58:56 | INFO     | backend.tasks:init_taskiq:89 - Initializing taskiq...
2025-06-10 17:58:56 | SUCCESS  | backend.tasks:init_taskiq:93 - Taskiq initialized successfully
2025-06-10 17:58:56 | SUCCESS  | backend.app:startup_event:133 - Motion Agent API with Taskiq initialized successfully
2025-06-10 17:58:56 | INFO     | backend.app:startup_event:134 - Motion Agent API is ready to serve requests
2025-06-10 17:59:51 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-10 18:00:51 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 13:09:07 | SUCCESS  | backend.config:validate_config:309 - Configuration validation passed
2025-06-11 13:09:07 | INFO     | backend.app:lifespan:78 - Starting Motion Agent API server with Taskiq...
2025-06-11 13:09:07 | INFO     | backend.app:lifespan:81 - Initializing MongoDB database...
2025-06-11 13:09:07 | INFO     | backend.database:init_database:50 - Initializing MongoDB database...
2025-06-11 13:09:07 | SUCCESS  | backend.database:init_database:81 - MongoDB database initialized successfully: motion_agent
2025-06-11 13:09:07 | INFO     | backend.app:lifespan:88 - Creating database indexes...
2025-06-11 13:09:07 | INFO     | backend.database:create_indexes:259 - Creating database indexes...
2025-06-11 13:09:07 | SUCCESS  | backend.database:create_indexes:279 - Database indexes created successfully
2025-06-11 13:09:07 | INFO     | backend.app:lifespan:92 - Initializing taskiq...
2025-06-11 13:09:07 | INFO     | backend.tasks:init_taskiq:89 - Initializing taskiq...
2025-06-11 13:09:07 | SUCCESS  | backend.tasks:init_taskiq:93 - Taskiq initialized successfully
2025-06-11 13:09:07 | SUCCESS  | backend.app:lifespan:102 - Motion Agent API with Taskiq initialized successfully
2025-06-11 13:09:07 | INFO     | backend.app:lifespan:103 - Motion Agent API is ready to serve requests
2025-06-11 13:09:20 | ERROR    | backend.routers.conversations:send_message:303 - Failed to send message to conversation 6848fdb8204a226de4b926fb: 'TaskService' object has no attribute 'submit_conversation_task'
2025-06-11 13:09:30 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 13:09:33 | INFO     | backend.routers.animation:generate_professional_animation:108 - Received professional animation request: walk forward then jump
2025-06-11 13:09:33 | INFO     | backend.services.task_service:create_task:69 - Created task: 4267245e-bb4f-4dcd-9a24-0e9dce6add6d (animation_generation)
2025-06-11 13:09:33 | INFO     | backend.services.task_service:create_animation_task:223 - Created animation task: 4267245e-bb4f-4dcd-9a24-0e9dce6add6d
2025-06-11 13:09:33 | INFO     | backend.routers.animation:generate_professional_animation:127 - Created animation task: 4267245e-bb4f-4dcd-9a24-0e9dce6add6d
2025-06-11 13:09:33 | INFO     | backend.services.task_service:update_task_status:262 - Updated task 4267245e-bb4f-4dcd-9a24-0e9dce6add6d status to running
2025-06-11 13:09:35 | WARNING  | backend.animation.professional_nlu:_load_spacy_model:55 - No spaCy model found, using blank model
2025-06-11 13:09:35 | INFO     | backend.animation.professional_nlu:_load_sentiment_model:64 - Using rule-based sentiment analysis instead of large model
2025-06-11 13:09:35 | INFO     | backend.animation.professional_nlu:_load_action_classifier:74 - Using rule-based action classification instead of large model
2025-06-11 13:09:35 | INFO     | backend.animation.professional_nlu:__init__:41 - Professional Animator NLU initialized
2025-06-11 13:09:35 | INFO     | backend.animation.animator_functions:__init__:37 - Unified Professional Animator initialized
2025-06-11 13:09:35 | INFO     | backend.animation.professional_pipeline:__init__:85 - Professional Animation Pipeline initialized with enhanced features
2025-06-11 13:09:35 | WARNING  | backend.animation.professional_nlu:_load_spacy_model:55 - No spaCy model found, using blank model
2025-06-11 13:09:35 | INFO     | backend.animation.professional_nlu:_load_sentiment_model:64 - Using rule-based sentiment analysis instead of large model
2025-06-11 13:09:35 | INFO     | backend.animation.professional_nlu:_load_action_classifier:74 - Using rule-based action classification instead of large model
2025-06-11 13:09:35 | INFO     | backend.animation.professional_nlu:__init__:41 - Professional Animator NLU initialized
2025-06-11 13:09:35 | INFO     | backend.animation.animator_functions:__init__:37 - Unified Professional Animator initialized
2025-06-11 13:09:35 | INFO     | backend.animation.professional_pipeline:__init__:85 - Professional Animation Pipeline initialized with enhanced features
2025-06-11 13:09:35 | INFO     | backend.animation.professional_pipeline:process_animation_request:94 - Processing animation request: walk forward then jump
2025-06-11 13:09:35 | INFO     | backend.animation.professional_pipeline:process_animation_request:98 - Step 1: Natural Language Understanding
2025-06-11 13:09:35 | INFO     | backend.animation.professional_nlu:process_natural_language:397 - Processing animation request: walk forward then jump
2025-06-11 13:09:35 | SUCCESS  | backend.animation.professional_nlu:process_natural_language:426 - Successfully processed 1 actions
2025-06-11 13:09:35 | INFO     | backend.animation.professional_pipeline:process_animation_request:105 - Step 2: Animator Function Processing
2025-06-11 13:09:35 | INFO     | backend.animation.professional_pipeline:_apply_animator_functions:180 - Applying unified animator functions with enhanced features
2025-06-11 13:09:35 | INFO     | backend.animation.animator_functions:create_walk_cycle:66 - Creating walk cycle: None, AnimationIntensity.NORMAL, steps=0
2025-06-11 13:09:35 | INFO     | backend.animation.professional_pipeline:_apply_animator_functions:191 - Optimizing action transitions
2025-06-11 13:09:35 | INFO     | backend.animation.transition_optimizer:optimize_action_sequence:146 - Optimizing sequence of 1 actions...
2025-06-11 13:09:35 | INFO     | backend.animation.professional_pipeline:process_animation_request:111 - Step 3: Generate Blender Animation Data
2025-06-11 13:09:35 | INFO     | backend.animation.professional_pipeline:process_animation_request:115 - Step 4: Execute Blender Animation Generation
2025-06-11 13:09:35 | INFO     | backend.animation.professional_pipeline:_execute_blender_generation:382 - Executing Blender command: /Applications/Blender.app/Contents/MacOS/Blender --background --python blender_scripts/generate_animation.py -- --input temp/animation_data/animation_data_1749618575.json --output output/animations/animation_test_character_1749618575.fbx --format fbx
2025-06-11 13:09:36 | SUCCESS  | backend.animation.professional_pipeline:_execute_blender_generation:394 - Blender animation generated: output/animations/animation_test_character_1749618575.fbx
2025-06-11 13:09:36 | INFO     | backend.animation.professional_pipeline:process_animation_request:126 - Step 5: FBX File Validation
2025-06-11 13:09:36 | INFO     | backend.animation.fbx_validator:validate_fbx_file:26 - Starting FBX validation for: output/animations/animation_test_character_1749618575.fbx
2025-06-11 13:09:36 | SUCCESS  | backend.animation.fbx_validator:validate_fbx_file:85 - FBX validation completed for: output/animations/animation_test_character_1749618575.fbx
2025-06-11 13:09:36 | INFO     | backend.animation.professional_pipeline:process_animation_request:135 - Step 6: Animation Quality Check
2025-06-11 13:09:36 | INFO     | backend.animation.quality_checker:check_animation_quality:130 - Starting animation quality check...
2025-06-11 13:09:36 | SUCCESS  | backend.animation.quality_checker:check_animation_quality:171 - Quality check completed. Overall score: 0.67
2025-06-11 13:09:36 | SUCCESS  | backend.animation.professional_pipeline:process_animation_request:160 - Animation generated successfully in 1.17s
2025-06-11 13:09:36 | INFO     | backend.services.task_service:update_task_status:262 - Updated task 4267245e-bb4f-4dcd-9a24-0e9dce6add6d status to completed
2025-06-11 13:09:36 | INFO     | backend.routers.animation:generate_professional_animation:205 - Updated task 4267245e-bb4f-4dcd-9a24-0e9dce6add6d status to completed
2025-06-11 13:09:36 | SUCCESS  | backend.routers.animation:generate_professional_animation:209 - Professional animation generation completed
2025-06-11 13:09:42 | ERROR    | backend.routers.conversations:send_message:303 - Failed to send message to conversation 6848fdb8204a226de4b926fb: 'TaskService' object has no attribute 'submit_conversation_task'
2025-06-11 13:09:51 | INFO     | backend.routers.animation:generate_professional_animation:108 - Received professional animation request: walk forward, then jump high, then attack with right hand
2025-06-11 13:09:51 | INFO     | backend.services.task_service:create_task:69 - Created task: 265affa7-236e-45a0-8464-91b2a38fc54b (animation_generation)
2025-06-11 13:09:51 | INFO     | backend.services.task_service:create_animation_task:223 - Created animation task: 265affa7-236e-45a0-8464-91b2a38fc54b
2025-06-11 13:09:51 | INFO     | backend.routers.animation:generate_professional_animation:127 - Created animation task: 265affa7-236e-45a0-8464-91b2a38fc54b
2025-06-11 13:09:51 | INFO     | backend.services.task_service:update_task_status:262 - Updated task 265affa7-236e-45a0-8464-91b2a38fc54b status to running
2025-06-11 13:09:51 | WARNING  | backend.animation.professional_nlu:_load_spacy_model:55 - No spaCy model found, using blank model
2025-06-11 13:09:51 | INFO     | backend.animation.professional_nlu:_load_sentiment_model:64 - Using rule-based sentiment analysis instead of large model
2025-06-11 13:09:51 | INFO     | backend.animation.professional_nlu:_load_action_classifier:74 - Using rule-based action classification instead of large model
2025-06-11 13:09:51 | INFO     | backend.animation.professional_nlu:__init__:41 - Professional Animator NLU initialized
2025-06-11 13:09:51 | INFO     | backend.animation.animator_functions:__init__:37 - Unified Professional Animator initialized
2025-06-11 13:09:51 | INFO     | backend.animation.professional_pipeline:__init__:85 - Professional Animation Pipeline initialized with enhanced features
2025-06-11 13:09:51 | INFO     | backend.animation.professional_pipeline:process_animation_request:94 - Processing animation request: walk forward, then jump high, then attack with right hand
2025-06-11 13:09:51 | INFO     | backend.animation.professional_pipeline:process_animation_request:98 - Step 1: Natural Language Understanding
2025-06-11 13:09:51 | INFO     | backend.animation.professional_nlu:process_natural_language:397 - Processing animation request: walk forward, then jump high, then attack with right hand
2025-06-11 13:09:51 | SUCCESS  | backend.animation.professional_nlu:process_natural_language:426 - Successfully processed 1 actions
2025-06-11 13:09:51 | INFO     | backend.animation.professional_pipeline:process_animation_request:105 - Step 2: Animator Function Processing
2025-06-11 13:09:51 | INFO     | backend.animation.professional_pipeline:_apply_animator_functions:180 - Applying unified animator functions with enhanced features
2025-06-11 13:09:51 | INFO     | backend.animation.animator_functions:create_walk_cycle:66 - Creating walk cycle: None, AnimationIntensity.NORMAL, steps=0
2025-06-11 13:09:51 | INFO     | backend.animation.professional_pipeline:_apply_animator_functions:191 - Optimizing action transitions
2025-06-11 13:09:51 | INFO     | backend.animation.transition_optimizer:optimize_action_sequence:146 - Optimizing sequence of 1 actions...
2025-06-11 13:09:51 | INFO     | backend.animation.professional_pipeline:process_animation_request:111 - Step 3: Generate Blender Animation Data
2025-06-11 13:09:51 | INFO     | backend.animation.professional_pipeline:process_animation_request:115 - Step 4: Execute Blender Animation Generation
2025-06-11 13:09:51 | INFO     | backend.animation.professional_pipeline:_execute_blender_generation:382 - Executing Blender command: /Applications/Blender.app/Contents/MacOS/Blender --background --python blender_scripts/generate_animation.py -- --input temp/animation_data/animation_data_1749618591.json --output output/animations/animation_test_character_1749618591.fbx --format fbx
2025-06-11 13:09:51 | SUCCESS  | backend.animation.professional_pipeline:_execute_blender_generation:394 - Blender animation generated: output/animations/animation_test_character_1749618591.fbx
2025-06-11 13:09:51 | INFO     | backend.animation.professional_pipeline:process_animation_request:126 - Step 5: FBX File Validation
2025-06-11 13:09:51 | INFO     | backend.animation.fbx_validator:validate_fbx_file:26 - Starting FBX validation for: output/animations/animation_test_character_1749618591.fbx
2025-06-11 13:09:51 | SUCCESS  | backend.animation.fbx_validator:validate_fbx_file:85 - FBX validation completed for: output/animations/animation_test_character_1749618591.fbx
2025-06-11 13:09:51 | INFO     | backend.animation.professional_pipeline:process_animation_request:135 - Step 6: Animation Quality Check
2025-06-11 13:09:51 | INFO     | backend.animation.quality_checker:check_animation_quality:130 - Starting animation quality check...
2025-06-11 13:09:51 | SUCCESS  | backend.animation.quality_checker:check_animation_quality:171 - Quality check completed. Overall score: 0.67
2025-06-11 13:09:51 | SUCCESS  | backend.animation.professional_pipeline:process_animation_request:160 - Animation generated successfully in 0.48s
2025-06-11 13:09:51 | INFO     | backend.services.task_service:update_task_status:262 - Updated task 265affa7-236e-45a0-8464-91b2a38fc54b status to completed
2025-06-11 13:09:51 | INFO     | backend.routers.animation:generate_professional_animation:205 - Updated task 265affa7-236e-45a0-8464-91b2a38fc54b status to completed
2025-06-11 13:09:51 | SUCCESS  | backend.routers.animation:generate_professional_animation:209 - Professional animation generation completed
2025-06-11 13:10:00 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 13:12:38 | SUCCESS  | backend.config:validate_config:309 - Configuration validation passed
2025-06-11 13:12:38 | INFO     | backend.app:lifespan:78 - Starting Motion Agent API server with Taskiq...
2025-06-11 13:12:38 | INFO     | backend.app:lifespan:81 - Initializing MongoDB database...
2025-06-11 13:12:38 | INFO     | backend.database:init_database:50 - Initializing MongoDB database...
2025-06-11 13:12:38 | SUCCESS  | backend.database:init_database:81 - MongoDB database initialized successfully: motion_agent
2025-06-11 13:12:38 | INFO     | backend.app:lifespan:88 - Creating database indexes...
2025-06-11 13:12:38 | INFO     | backend.database:create_indexes:259 - Creating database indexes...
2025-06-11 13:12:38 | SUCCESS  | backend.database:create_indexes:279 - Database indexes created successfully
2025-06-11 13:12:38 | INFO     | backend.app:lifespan:92 - Initializing taskiq...
2025-06-11 13:12:38 | INFO     | backend.tasks:init_taskiq:89 - Initializing taskiq...
2025-06-11 13:12:38 | SUCCESS  | backend.tasks:init_taskiq:93 - Taskiq initialized successfully
2025-06-11 13:12:38 | SUCCESS  | backend.app:lifespan:102 - Motion Agent API with Taskiq initialized successfully
2025-06-11 13:12:38 | INFO     | backend.app:lifespan:103 - Motion Agent API is ready to serve requests
2025-06-11 13:13:05 | INFO     | backend.routers.conversations:list_conversations:107 - Listed 3 conversations
2025-06-11 13:13:12 | INFO     | backend.services.task_service:create_task:69 - Created task: 0d44babf-f376-4cae-af1e-49a5e2f2d6ee (conversation_processing)
2025-06-11 13:13:12 | INFO     | backend.services.task_service:submit_conversation_task:259 - Created conversation task: 0d44babf-f376-4cae-af1e-49a5e2f2d6ee
2025-06-11 13:13:12 | INFO     | backend.routers.conversations:send_message:291 - Submitted message processing task: 0d44babf-f376-4cae-af1e-49a5e2f2d6ee
2025-06-11 13:13:13 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 13:13:26 | INFO     | backend.services.task_service:create_task:69 - Created task: ef77d1ce-3df8-4533-99fb-957eda0f968e (conversation_processing)
2025-06-11 13:13:26 | INFO     | backend.services.task_service:submit_conversation_task:259 - Created conversation task: ef77d1ce-3df8-4533-99fb-957eda0f968e
2025-06-11 13:13:26 | INFO     | backend.routers.conversations:send_message:291 - Submitted message processing task: ef77d1ce-3df8-4533-99fb-957eda0f968e
2025-06-11 13:13:29 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 13:13:30 | INFO     | backend.services.task_service:create_task:69 - Created task: 379ff6e5-ed24-4695-916c-fc13a3c5834d (conversation_processing)
2025-06-11 13:13:30 | INFO     | backend.services.task_service:submit_conversation_task:259 - Created conversation task: 379ff6e5-ed24-4695-916c-fc13a3c5834d
2025-06-11 13:13:30 | INFO     | backend.routers.conversations:send_message:291 - Submitted message processing task: 379ff6e5-ed24-4695-916c-fc13a3c5834d
2025-06-11 13:13:43 | INFO     | backend.services.task_service:create_task:69 - Created task: 1d9cd38e-30cd-4e96-890e-4b72bc1ffd51 (conversation_processing)
2025-06-11 13:13:43 | INFO     | backend.services.task_service:submit_conversation_task:259 - Created conversation task: 1d9cd38e-30cd-4e96-890e-4b72bc1ffd51
2025-06-11 13:13:43 | INFO     | backend.routers.conversations:send_message:291 - Submitted message processing task: 1d9cd38e-30cd-4e96-890e-4b72bc1ffd51
2025-06-11 13:13:48 | INFO     | backend.services.task_service:create_task:69 - Created task: 8caf86cb-3afc-4985-a298-9c86da801305 (animation_generation)
2025-06-11 13:13:48 | INFO     | backend.services.task_service:submit_animation_task:295 - Created animation task for conversation: 8caf86cb-3afc-4985-a298-9c86da801305
2025-06-11 13:13:48 | INFO     | backend.routers.conversations:start_animation_generation:359 - Submitted animation generation task: 8caf86cb-3afc-4985-a298-9c86da801305
2025-06-11 13:14:00 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 13:19:57 | SUCCESS  | backend.config:validate_config:309 - Configuration validation passed
2025-06-11 13:19:57 | INFO     | backend.app:lifespan:78 - Starting Motion Agent API server with Taskiq...
2025-06-11 13:19:57 | INFO     | backend.app:lifespan:81 - Initializing MongoDB database...
2025-06-11 13:19:57 | INFO     | backend.database:init_database:50 - Initializing MongoDB database...
2025-06-11 13:19:57 | SUCCESS  | backend.database:init_database:81 - MongoDB database initialized successfully: motion_agent
2025-06-11 13:19:57 | INFO     | backend.app:lifespan:88 - Creating database indexes...
2025-06-11 13:19:57 | INFO     | backend.database:create_indexes:259 - Creating database indexes...
2025-06-11 13:19:57 | SUCCESS  | backend.database:create_indexes:279 - Database indexes created successfully
2025-06-11 13:19:57 | INFO     | backend.app:lifespan:92 - Initializing taskiq...
2025-06-11 13:19:57 | INFO     | backend.tasks:init_taskiq:89 - Initializing taskiq...
2025-06-11 13:19:57 | SUCCESS  | backend.tasks:init_taskiq:93 - Taskiq initialized successfully
2025-06-11 13:19:57 | SUCCESS  | backend.app:lifespan:102 - Motion Agent API with Taskiq initialized successfully
2025-06-11 13:19:57 | INFO     | backend.app:lifespan:103 - Motion Agent API is ready to serve requests
2025-06-11 13:20:17 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 13:20:24 | INFO     | backend.services.task_service:create_task:69 - Created task: b24df3f3-17f1-4553-b935-edfc716a4221 (conversation_processing)
2025-06-11 13:20:24 | INFO     | backend.services.task_service:submit_conversation_task:259 - Created conversation task: b24df3f3-17f1-4553-b935-edfc716a4221
2025-06-11 13:20:24 | INFO     | backend.services.task_service:submit_conversation_task:274 - Submitted conversation task to queue: b24df3f3-17f1-4553-b935-edfc716a4221
2025-06-11 13:20:24 | INFO     | backend.routers.conversations:send_message:291 - Submitted message processing task: b24df3f3-17f1-4553-b935-edfc716a4221
2025-06-11 13:20:44 | INFO     | backend.app:lifespan:107 - Shutting down Motion Agent API...
2025-06-11 13:20:44 | INFO     | backend.tasks:shutdown_taskiq:104 - Shutting down taskiq...
2025-06-11 13:20:44 | SUCCESS  | backend.tasks:shutdown_taskiq:106 - Taskiq shutdown successfully
2025-06-11 13:20:44 | INFO     | backend.database:close_database:94 - Closing MongoDB connections...
2025-06-11 13:20:44 | SUCCESS  | backend.database:close_database:101 - MongoDB connections closed
2025-06-11 13:20:44 | SUCCESS  | backend.app:lifespan:115 - Motion Agent API shutdown completed
2025-06-11 13:20:45 | SUCCESS  | backend.config:validate_config:309 - Configuration validation passed
2025-06-11 13:20:45 | INFO     | backend.app:lifespan:78 - Starting Motion Agent API server with Taskiq...
2025-06-11 13:20:45 | INFO     | backend.app:lifespan:81 - Initializing MongoDB database...
2025-06-11 13:20:45 | INFO     | backend.database:init_database:50 - Initializing MongoDB database...
2025-06-11 13:20:45 | SUCCESS  | backend.database:init_database:81 - MongoDB database initialized successfully: motion_agent
2025-06-11 13:20:45 | INFO     | backend.app:lifespan:88 - Creating database indexes...
2025-06-11 13:20:45 | INFO     | backend.database:create_indexes:259 - Creating database indexes...
2025-06-11 13:20:45 | SUCCESS  | backend.database:create_indexes:279 - Database indexes created successfully
2025-06-11 13:20:45 | INFO     | backend.app:lifespan:92 - Initializing taskiq...
2025-06-11 13:20:45 | INFO     | backend.tasks:init_taskiq:89 - Initializing taskiq...
2025-06-11 13:20:45 | SUCCESS  | backend.tasks:init_taskiq:93 - Taskiq initialized successfully
2025-06-11 13:20:45 | SUCCESS  | backend.app:lifespan:102 - Motion Agent API with Taskiq initialized successfully
2025-06-11 13:20:45 | INFO     | backend.app:lifespan:103 - Motion Agent API is ready to serve requests
2025-06-11 13:20:47 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 13:21:17 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 13:21:31 | INFO     | backend.services.task_service:create_task:69 - Created task: 0ccd2cd1-c861-4979-b9ff-5442f4d14332 (conversation_processing)
2025-06-11 13:21:31 | INFO     | backend.services.task_service:submit_conversation_task:259 - Created conversation task: 0ccd2cd1-c861-4979-b9ff-5442f4d14332
2025-06-11 13:21:31 | INFO     | backend.services.task_service:submit_conversation_task:274 - Submitted conversation task to queue: 0ccd2cd1-c861-4979-b9ff-5442f4d14332
2025-06-11 13:21:31 | INFO     | backend.routers.conversations:send_message:291 - Submitted message processing task: 0ccd2cd1-c861-4979-b9ff-5442f4d14332
2025-06-11 13:21:48 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 13:22:17 | INFO     | backend.services.task_service:create_task:69 - Created task: b31ca05e-163d-4e75-b2d9-c29eb7981d05 (conversation_processing)
2025-06-11 13:22:17 | INFO     | backend.services.task_service:submit_conversation_task:259 - Created conversation task: b31ca05e-163d-4e75-b2d9-c29eb7981d05
2025-06-11 13:22:17 | INFO     | backend.services.task_service:submit_conversation_task:274 - Submitted conversation task to queue: b31ca05e-163d-4e75-b2d9-c29eb7981d05
2025-06-11 13:22:17 | INFO     | backend.routers.conversations:send_message:291 - Submitted message processing task: b31ca05e-163d-4e75-b2d9-c29eb7981d05
2025-06-11 13:22:48 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 13:23:11 | INFO     | backend.services.task_service:create_task:69 - Created task: 1d405392-e13f-4446-9a5f-de832989edff (conversation_processing)
2025-06-11 13:23:11 | INFO     | backend.services.task_service:submit_conversation_task:259 - Created conversation task: 1d405392-e13f-4446-9a5f-de832989edff
2025-06-11 13:23:11 | INFO     | backend.services.task_service:submit_conversation_task:274 - Submitted conversation task to queue: 1d405392-e13f-4446-9a5f-de832989edff
2025-06-11 13:23:11 | INFO     | backend.routers.conversations:send_message:291 - Submitted message processing task: 1d405392-e13f-4446-9a5f-de832989edff
2025-06-11 13:23:48 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 13:24:48 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 13:25:17 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 13:25:17 | INFO     | backend.services.task_service:create_task:69 - Created task: 07e94524-ec7d-4635-a042-4ac488cb2c23 (conversation_processing)
2025-06-11 13:25:17 | INFO     | backend.services.task_service:submit_conversation_task:259 - Created conversation task: 07e94524-ec7d-4635-a042-4ac488cb2c23
2025-06-11 13:25:17 | INFO     | backend.services.task_service:submit_conversation_task:274 - Submitted conversation task to queue: 07e94524-ec7d-4635-a042-4ac488cb2c23
2025-06-11 13:25:17 | INFO     | backend.routers.conversations:send_message:291 - Submitted message processing task: 07e94524-ec7d-4635-a042-4ac488cb2c23
2025-06-11 13:25:47 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 13:26:17 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 13:26:48 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 13:27:48 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 13:29:17 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 13:30:17 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 13:30:30 | INFO     | backend.services.task_service:create_task:69 - Created task: a87b8349-9a52-4506-a556-be0f4c85b336 (conversation_processing)
2025-06-11 13:30:30 | INFO     | backend.services.task_service:submit_conversation_task:259 - Created conversation task: a87b8349-9a52-4506-a556-be0f4c85b336
2025-06-11 13:30:30 | INFO     | backend.services.task_service:submit_conversation_task:274 - Submitted conversation task to queue: a87b8349-9a52-4506-a556-be0f4c85b336
2025-06-11 13:30:30 | INFO     | backend.routers.conversations:send_message:291 - Submitted message processing task: a87b8349-9a52-4506-a556-be0f4c85b336
2025-06-11 13:31:17 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 13:31:28 | INFO     | backend.app:lifespan:107 - Shutting down Motion Agent API...
2025-06-11 13:31:28 | INFO     | backend.tasks:shutdown_taskiq:104 - Shutting down taskiq...
2025-06-11 13:31:28 | SUCCESS  | backend.tasks:shutdown_taskiq:106 - Taskiq shutdown successfully
2025-06-11 13:31:28 | INFO     | backend.database:close_database:94 - Closing MongoDB connections...
2025-06-11 13:31:28 | SUCCESS  | backend.database:close_database:101 - MongoDB connections closed
2025-06-11 13:31:28 | SUCCESS  | backend.app:lifespan:115 - Motion Agent API shutdown completed
2025-06-11 13:31:29 | SUCCESS  | backend.config:validate_config:309 - Configuration validation passed
2025-06-11 13:31:29 | INFO     | backend.app:lifespan:78 - Starting Motion Agent API server with Taskiq...
2025-06-11 13:31:29 | INFO     | backend.app:lifespan:81 - Initializing MongoDB database...
2025-06-11 13:31:29 | INFO     | backend.database:init_database:50 - Initializing MongoDB database...
2025-06-11 13:31:29 | SUCCESS  | backend.database:init_database:81 - MongoDB database initialized successfully: motion_agent
2025-06-11 13:31:29 | INFO     | backend.app:lifespan:88 - Creating database indexes...
2025-06-11 13:31:29 | INFO     | backend.database:create_indexes:259 - Creating database indexes...
2025-06-11 13:31:29 | SUCCESS  | backend.database:create_indexes:279 - Database indexes created successfully
2025-06-11 13:31:29 | INFO     | backend.app:lifespan:92 - Initializing taskiq...
2025-06-11 13:31:29 | INFO     | backend.tasks:init_taskiq:89 - Initializing taskiq...
2025-06-11 13:31:29 | SUCCESS  | backend.tasks:init_taskiq:93 - Taskiq initialized successfully
2025-06-11 13:31:29 | SUCCESS  | backend.app:lifespan:102 - Motion Agent API with Taskiq initialized successfully
2025-06-11 13:31:29 | INFO     | backend.app:lifespan:103 - Motion Agent API is ready to serve requests
2025-06-11 13:31:47 | INFO     | backend.app:lifespan:107 - Shutting down Motion Agent API...
2025-06-11 13:31:47 | INFO     | backend.tasks:shutdown_taskiq:104 - Shutting down taskiq...
2025-06-11 13:31:47 | SUCCESS  | backend.tasks:shutdown_taskiq:106 - Taskiq shutdown successfully
2025-06-11 13:31:47 | INFO     | backend.database:close_database:94 - Closing MongoDB connections...
2025-06-11 13:31:47 | SUCCESS  | backend.database:close_database:101 - MongoDB connections closed
2025-06-11 13:31:47 | SUCCESS  | backend.app:lifespan:115 - Motion Agent API shutdown completed
2025-06-11 13:31:48 | SUCCESS  | backend.config:validate_config:309 - Configuration validation passed
2025-06-11 13:31:48 | INFO     | backend.app:lifespan:78 - Starting Motion Agent API server with Taskiq...
2025-06-11 13:31:48 | INFO     | backend.app:lifespan:81 - Initializing MongoDB database...
2025-06-11 13:31:48 | INFO     | backend.database:init_database:50 - Initializing MongoDB database...
2025-06-11 13:31:48 | SUCCESS  | backend.database:init_database:81 - MongoDB database initialized successfully: motion_agent
2025-06-11 13:31:48 | INFO     | backend.app:lifespan:88 - Creating database indexes...
2025-06-11 13:31:48 | INFO     | backend.database:create_indexes:259 - Creating database indexes...
2025-06-11 13:31:48 | SUCCESS  | backend.database:create_indexes:279 - Database indexes created successfully
2025-06-11 13:31:48 | INFO     | backend.app:lifespan:92 - Initializing taskiq...
2025-06-11 13:31:48 | INFO     | backend.tasks:init_taskiq:89 - Initializing taskiq...
2025-06-11 13:31:48 | SUCCESS  | backend.tasks:init_taskiq:93 - Taskiq initialized successfully
2025-06-11 13:31:48 | SUCCESS  | backend.app:lifespan:102 - Motion Agent API with Taskiq initialized successfully
2025-06-11 13:31:48 | INFO     | backend.app:lifespan:103 - Motion Agent API is ready to serve requests
2025-06-11 13:32:17 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 13:33:16 | INFO     | backend.app:lifespan:107 - Shutting down Motion Agent API...
2025-06-11 13:33:16 | INFO     | backend.tasks:shutdown_taskiq:104 - Shutting down taskiq...
2025-06-11 13:33:16 | SUCCESS  | backend.tasks:shutdown_taskiq:106 - Taskiq shutdown successfully
2025-06-11 13:33:16 | INFO     | backend.database:close_database:94 - Closing MongoDB connections...
2025-06-11 13:33:16 | SUCCESS  | backend.database:close_database:101 - MongoDB connections closed
2025-06-11 13:33:16 | SUCCESS  | backend.app:lifespan:115 - Motion Agent API shutdown completed
2025-06-11 13:33:16 | SUCCESS  | backend.config:validate_config:309 - Configuration validation passed
2025-06-11 13:33:16 | INFO     | backend.app:lifespan:78 - Starting Motion Agent API server with Taskiq...
2025-06-11 13:33:16 | INFO     | backend.app:lifespan:81 - Initializing MongoDB database...
2025-06-11 13:33:16 | INFO     | backend.database:init_database:50 - Initializing MongoDB database...
2025-06-11 13:33:16 | SUCCESS  | backend.database:init_database:81 - MongoDB database initialized successfully: motion_agent
2025-06-11 13:33:16 | INFO     | backend.app:lifespan:88 - Creating database indexes...
2025-06-11 13:33:16 | INFO     | backend.database:create_indexes:259 - Creating database indexes...
2025-06-11 13:33:16 | SUCCESS  | backend.database:create_indexes:279 - Database indexes created successfully
2025-06-11 13:33:16 | INFO     | backend.app:lifespan:92 - Initializing taskiq...
2025-06-11 13:33:16 | INFO     | backend.tasks:init_taskiq:89 - Initializing taskiq...
2025-06-11 13:33:16 | SUCCESS  | backend.tasks:init_taskiq:93 - Taskiq initialized successfully
2025-06-11 13:33:16 | SUCCESS  | backend.app:lifespan:102 - Motion Agent API with Taskiq initialized successfully
2025-06-11 13:33:16 | INFO     | backend.app:lifespan:103 - Motion Agent API is ready to serve requests
2025-06-11 13:33:17 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 13:33:56 | INFO     | backend.services.task_service:create_task:69 - Created task: ea7c48d5-36fa-4aaf-9062-48a944b9c498 (conversation_processing)
2025-06-11 13:33:56 | INFO     | backend.services.task_service:submit_conversation_task:259 - Created conversation task: ea7c48d5-36fa-4aaf-9062-48a944b9c498
2025-06-11 13:33:56 | INFO     | backend.services.task_service:submit_conversation_task:274 - Submitted conversation task to queue: ea7c48d5-36fa-4aaf-9062-48a944b9c498
2025-06-11 13:33:56 | INFO     | backend.routers.conversations:send_message:291 - Submitted message processing task: ea7c48d5-36fa-4aaf-9062-48a944b9c498
2025-06-11 13:34:17 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 13:35:17 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 13:36:17 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 13:37:17 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 13:38:17 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 13:39:17 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 13:39:46 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 13:40:15 | INFO     | backend.services.task_service:create_task:69 - Created task: 9f5d6084-04a6-4030-96ea-1a3aa16d27ec (conversation_processing)
2025-06-11 13:40:15 | INFO     | backend.services.task_service:submit_conversation_task:259 - Created conversation task: 9f5d6084-04a6-4030-96ea-1a3aa16d27ec
2025-06-11 13:40:15 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 13:40:15 | INFO     | backend.services.task_service:submit_conversation_task:274 - Submitted conversation task to queue: 9f5d6084-04a6-4030-96ea-1a3aa16d27ec
2025-06-11 13:40:15 | INFO     | backend.routers.conversations:send_message:291 - Submitted message processing task: 9f5d6084-04a6-4030-96ea-1a3aa16d27ec
2025-06-11 13:40:29 | INFO     | backend.routers.conversations:list_conversations:107 - Listed 8 conversations
2025-06-11 13:40:29 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
2025-06-11 13:40:29 | INFO     | backend.routers.conversations:create_conversation:51 - Creating new conversation: New Conversation 6/11/2025, 1:40:29 PM
2025-06-11 13:40:29 | INFO     | backend.services.conversation_service:create_conversation:54 - Created new conversation: 684916cde08c7c0c02f1cf52
2025-06-11 13:40:29 | SUCCESS  | backend.routers.conversations:create_conversation:55 - Created conversation: 684916cde08c7c0c02f1cf52
2025-06-11 13:40:31 | INFO     | backend.services.task_service:create_task:69 - Created task: 723be295-a2ef-46b0-af7b-04b2053db73c (conversation_processing)
2025-06-11 13:40:31 | INFO     | backend.services.task_service:submit_conversation_task:259 - Created conversation task: 723be295-a2ef-46b0-af7b-04b2053db73c
2025-06-11 13:40:31 | INFO     | backend.services.task_service:submit_conversation_task:274 - Submitted conversation task to queue: 723be295-a2ef-46b0-af7b-04b2053db73c
2025-06-11 13:40:31 | INFO     | backend.routers.conversations:send_message:291 - Submitted message processing task: 723be295-a2ef-46b0-af7b-04b2053db73c
2025-06-11 13:40:50 | INFO     | backend.services.task_service:create_task:69 - Created task: 3d664a72-b467-43e7-8875-5270ebc6fadb (conversation_processing)
2025-06-11 13:40:50 | INFO     | backend.services.task_service:submit_conversation_task:259 - Created conversation task: 3d664a72-b467-43e7-8875-5270ebc6fadb
2025-06-11 13:40:50 | INFO     | backend.services.task_service:submit_conversation_task:274 - Submitted conversation task to queue: 3d664a72-b467-43e7-8875-5270ebc6fadb
2025-06-11 13:40:50 | INFO     | backend.routers.conversations:send_message:291 - Submitted message processing task: 3d664a72-b467-43e7-8875-5270ebc6fadb
2025-06-11 13:40:59 | INFO     | backend.routers.health:root:18 - Root health endpoint accessed
