#!/usr/bin/env python3
"""
Taskiq Worker 启动脚本
Taskiq Worker Startup Script
"""

import asyncio
import os
import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from loguru import logger

from backend.tasks import broker


def setup_logging():
    """设置日志"""
    logger.remove()  # 移除默认处理器

    # 控制台日志
    logger.add(
        sys.stdout,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        level="INFO",
    )

    # 文件日志
    logger.add(
        "logs/worker.log",
        rotation="10 MB",
        retention="7 days",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
        level="DEBUG",
    )


async def main():
    """主函数"""
    setup_logging()

    logger.info("Starting Taskiq Worker...")

    try:
        # 导入所有任务模块以确保任务被注册
        from backend.tasks import animation_tasks, conversation_tasks

        logger.info("Imported task modules:")
        logger.info("- animation_tasks")
        logger.info("- conversation_tasks")

        # 启动 worker
        logger.info("Starting worker with broker...")
        await broker.startup()

        # 运行 worker
        logger.success("Taskiq Worker started successfully")
        logger.info("Worker is ready to process tasks...")

        # 保持运行
        try:
            while True:
                await asyncio.sleep(1)
        except KeyboardInterrupt:
            logger.info("Received shutdown signal")

    except Exception as e:
        logger.error(f"Failed to start worker: {e}")
        sys.exit(1)

    finally:
        logger.info("Shutting down worker...")
        await broker.shutdown()
        logger.success("Worker shutdown completed")


if __name__ == "__main__":
    # 确保日志目录存在
    os.makedirs("logs", exist_ok=True)

    # 运行主函数
    asyncio.run(main())
