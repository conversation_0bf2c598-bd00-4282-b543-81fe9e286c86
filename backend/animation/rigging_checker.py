"""
骨骼绑定质量检查器
Rigging Quality Checker for Professional Animation Standards
"""

from dataclasses import dataclass
from enum import Enum
from typing import Any

import numpy as np
from loguru import logger


class RiggingIssueType(Enum):
    """绑定问题类型"""
    BONE_HIERARCHY = "bone_hierarchy"
    WEIGHT_PAINTING = "weight_painting"
    CONSTRAINT_SETUP = "constraint_setup"
    IK_FK_SETUP = "ik_fk_setup"
    DEFORMATION = "deformation"
    NAMING_CONVENTION = "naming_convention"
    PERFORMANCE = "performance"


class SeverityLevel(Enum):
    """严重程度"""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    INFO = "info"


@dataclass
class RiggingIssue:
    """绑定问题"""
    issue_type: RiggingIssueType
    severity: SeverityLevel
    bone_name: str
    description: str
    suggested_fix: str
    affects_animation: bool = True
    performance_impact: float = 0.0  # 0-1 scale


@dataclass
class BoneInfo:
    """骨骼信息"""
    name: str
    parent: str | None
    children: list[str]
    position: tuple[float, float, float]
    rotation: tuple[float, float, float]
    length: float
    weight_count: int
    has_constraints: bool
    is_deform_bone: bool


@dataclass
class RiggingMetrics:
    """绑定指标"""
    total_bones: int
    deform_bones: int
    control_bones: int
    ik_chains: int
    constraints_count: int
    weight_quality_score: float
    hierarchy_score: float
    naming_score: float
    performance_score: float
    overall_score: float


class RiggingQualityChecker:
    """骨骼绑定质量检查器"""

    def __init__(self):
        self.naming_conventions = self._load_naming_conventions()
        self.bone_standards = self._load_bone_standards()
        self.weight_standards = self._load_weight_standards()

    def _load_naming_conventions(self) -> dict[str, Any]:
        """加载命名规范"""
        return {
            "prefixes": {
                "left": ["L_", "l_", "Left_", "left_"],
                "right": ["R_", "r_", "Right_", "right_"],
                "center": ["C_", "c_", "Center_", "center_", "M_", "m_"]
            },
            "suffixes": {
                "control": ["_ctrl", "_control", "_ctl"],
                "joint": ["_jnt", "_joint", "_bone"],
                "ik": ["_ik", "_IK"],
                "fk": ["_fk", "_FK"]
            },
            "required_bones": [
                "root", "spine", "chest", "neck", "head",
                "shoulder_l", "shoulder_r", "arm_l", "arm_r",
                "forearm_l", "forearm_r", "hand_l", "hand_r",
                "thigh_l", "thigh_r", "calf_l", "calf_r",
                "foot_l", "foot_r"
            ]
        }

    def _load_bone_standards(self) -> dict[str, Any]:
        """加载骨骼标准"""
        return {
            "max_bones": 500,  # 最大骨骼数
            "max_hierarchy_depth": 10,  # 最大层级深度
            "min_bone_length": 0.001,  # 最小骨骼长度
            "max_bone_length": 100.0,  # 最大骨骼长度
            "ideal_ik_chain_length": (2, 4),  # 理想IK链长度
            "max_influences_per_vertex": 4  # 每个顶点最大影响骨骼数
        }

    def _load_weight_standards(self) -> dict[str, Any]:
        """加载权重标准"""
        return {
            "min_weight_threshold": 0.001,  # 最小权重阈值
            "max_weight_per_bone": 1.0,  # 每个骨骼最大权重
            "weight_normalization_tolerance": 0.001,  # 权重归一化容差
            "smooth_weight_threshold": 0.1,  # 平滑权重阈值
            "max_zero_weights": 0.05  # 最大零权重比例
        }

    def check_rigging_quality(self, rigging_data: dict[str, Any]) -> dict[str, Any]:
        """检查绑定质量"""
        logger.info("Starting rigging quality check...")

        result = {
            "overall_score": 0.0,
            "metrics": {},
            "issues": [],
            "recommendations": [],
            "bone_analysis": {},
            "weight_analysis": {},
            "performance_analysis": {}
        }

        try:
            # 1. 解析骨骼数据
            bones = self._parse_bone_data(rigging_data)

            # 2. 计算基础指标
            metrics = self._calculate_rigging_metrics(bones, rigging_data)
            result["metrics"] = metrics.__dict__

            # 3. 检查骨骼层级
            hierarchy_issues = self._check_bone_hierarchy(bones)

            # 4. 检查命名规范
            naming_issues = self._check_naming_conventions(bones)

            # 5. 检查权重绘制
            weight_issues = self._check_weight_painting(rigging_data)

            # 6. 检查约束设置
            constraint_issues = self._check_constraints(rigging_data)

            # 7. 检查IK/FK设置
            ik_fk_issues = self._check_ik_fk_setup(rigging_data)

            # 8. 检查变形质量
            deformation_issues = self._check_deformation_quality(rigging_data)

            # 9. 检查性能影响
            performance_issues = self._check_performance_impact(bones, rigging_data)

            # 合并所有问题
            all_issues = (hierarchy_issues + naming_issues + weight_issues +
                         constraint_issues + ik_fk_issues + deformation_issues +
                         performance_issues)

            result["issues"] = [issue.__dict__ for issue in all_issues]

            # 10. 计算总体评分
            result["overall_score"] = self._calculate_overall_score(metrics, all_issues)

            # 11. 生成建议
            result["recommendations"] = self._generate_rigging_recommendations(metrics, all_issues)

            # 12. 详细分析
            result["bone_analysis"] = self._analyze_bone_structure(bones)
            result["weight_analysis"] = self._analyze_weight_distribution(rigging_data)
            result["performance_analysis"] = self._analyze_performance_impact(bones, rigging_data)

            logger.success(f"Rigging quality check completed. Score: {result['overall_score']:.2f}")
            return result

        except Exception as e:
            logger.error(f"Error during rigging quality check: {e}")
            result["error"] = str(e)
            return result

    def _parse_bone_data(self, rigging_data: dict[str, Any]) -> list[BoneInfo]:
        """解析骨骼数据"""
        bones = []

        # 模拟骨骼数据解析
        bone_data = rigging_data.get("bones", [])

        for bone_info in bone_data:
            bone = BoneInfo(
                name=bone_info.get("name", "unknown"),
                parent=bone_info.get("parent"),
                children=bone_info.get("children", []),
                position=tuple(bone_info.get("position", [0, 0, 0])),
                rotation=tuple(bone_info.get("rotation", [0, 0, 0])),
                length=bone_info.get("length", 1.0),
                weight_count=bone_info.get("weight_count", 0),
                has_constraints=bone_info.get("has_constraints", False),
                is_deform_bone=bone_info.get("is_deform_bone", True)
            )
            bones.append(bone)

        return bones

    def _calculate_rigging_metrics(self, bones: list[BoneInfo], rigging_data: dict[str, Any]) -> RiggingMetrics:
        """计算绑定指标"""
        total_bones = len(bones)
        deform_bones = sum(1 for bone in bones if bone.is_deform_bone)
        control_bones = total_bones - deform_bones

        # 计算IK链数量
        ik_chains = rigging_data.get("ik_chains", 0)

        # 计算约束数量
        constraints_count = sum(1 for bone in bones if bone.has_constraints)

        # 计算各项评分
        weight_quality_score = self._calculate_weight_quality_score(rigging_data)
        hierarchy_score = self._calculate_hierarchy_score(bones)
        naming_score = self._calculate_naming_score(bones)
        performance_score = self._calculate_performance_score(bones, rigging_data)

        # 计算总体评分
        overall_score = (
            weight_quality_score * 0.3 +
            hierarchy_score * 0.25 +
            naming_score * 0.15 +
            performance_score * 0.3
        )

        return RiggingMetrics(
            total_bones=total_bones,
            deform_bones=deform_bones,
            control_bones=control_bones,
            ik_chains=ik_chains,
            constraints_count=constraints_count,
            weight_quality_score=weight_quality_score,
            hierarchy_score=hierarchy_score,
            naming_score=naming_score,
            performance_score=performance_score,
            overall_score=overall_score
        )

    def _check_bone_hierarchy(self, bones: list[BoneInfo]) -> list[RiggingIssue]:
        """检查骨骼层级"""
        issues = []

        # 检查根骨骼
        root_bones = [bone for bone in bones if bone.parent is None]
        if len(root_bones) != 1:
            issues.append(RiggingIssue(
                issue_type=RiggingIssueType.BONE_HIERARCHY,
                severity=SeverityLevel.HIGH,
                bone_name="root",
                description=f"Found {len(root_bones)} root bones, should be exactly 1",
                suggested_fix="Ensure there is exactly one root bone in the hierarchy"
            ))

        # 检查层级深度
        max_depth = self._calculate_hierarchy_depth(bones)
        if max_depth > self.bone_standards["max_hierarchy_depth"]:
            issues.append(RiggingIssue(
                issue_type=RiggingIssueType.BONE_HIERARCHY,
                severity=SeverityLevel.MEDIUM,
                bone_name="hierarchy",
                description=f"Hierarchy depth {max_depth} exceeds recommended maximum {self.bone_standards['max_hierarchy_depth']}",
                suggested_fix="Consider flattening the bone hierarchy"
            ))

        # 检查骨骼长度
        for bone in bones:
            if bone.length < self.bone_standards["min_bone_length"]:
                issues.append(RiggingIssue(
                    issue_type=RiggingIssueType.BONE_HIERARCHY,
                    severity=SeverityLevel.LOW,
                    bone_name=bone.name,
                    description=f"Bone length {bone.length} is too small",
                    suggested_fix="Increase bone length or remove unnecessary bones"
                ))

        return issues

    def _check_naming_conventions(self, bones: list[BoneInfo]) -> list[RiggingIssue]:
        """检查命名规范"""
        issues = []

        # 检查必需骨骼
        bone_names = [bone.name.lower() for bone in bones]
        for required_bone in self.naming_conventions["required_bones"]:
            if not any(required_bone in name for name in bone_names):
                issues.append(RiggingIssue(
                    issue_type=RiggingIssueType.NAMING_CONVENTION,
                    severity=SeverityLevel.MEDIUM,
                    bone_name=required_bone,
                    description=f"Missing required bone: {required_bone}",
                    suggested_fix=f"Add {required_bone} bone to the rig"
                ))

        # 检查左右对称命名
        for bone in bones:
            if self._is_side_bone(bone.name):
                opposite_name = self._get_opposite_bone_name(bone.name)
                if not any(b.name == opposite_name for b in bones):
                    issues.append(RiggingIssue(
                        issue_type=RiggingIssueType.NAMING_CONVENTION,
                        severity=SeverityLevel.LOW,
                        bone_name=bone.name,
                        description=f"Missing opposite side bone for {bone.name}",
                        suggested_fix=f"Add corresponding bone: {opposite_name}"
                    ))

        return issues

    def _check_weight_painting(self, rigging_data: dict[str, Any]) -> list[RiggingIssue]:
        """检查权重绘制"""
        issues = []

        weight_data = rigging_data.get("weights", {})

        # 检查权重归一化
        for vertex_id, weights in weight_data.items():
            total_weight = sum(weights.values())
            if abs(total_weight - 1.0) > self.weight_standards["weight_normalization_tolerance"]:
                issues.append(RiggingIssue(
                    issue_type=RiggingIssueType.WEIGHT_PAINTING,
                    severity=SeverityLevel.HIGH,
                    bone_name=f"vertex_{vertex_id}",
                    description=f"Vertex weights not normalized: {total_weight}",
                    suggested_fix="Normalize vertex weights to sum to 1.0"
                ))

        # 检查影响数量
        for vertex_id, weights in weight_data.items():
            influence_count = len([w for w in weights.values() if w > self.weight_standards["min_weight_threshold"]])
            if influence_count > self.bone_standards["max_influences_per_vertex"]:
                issues.append(RiggingIssue(
                    issue_type=RiggingIssueType.WEIGHT_PAINTING,
                    severity=SeverityLevel.MEDIUM,
                    bone_name=f"vertex_{vertex_id}",
                    description=f"Too many bone influences: {influence_count}",
                    suggested_fix="Reduce bone influences per vertex for better performance"
                ))

        return issues

    def _check_constraints(self, rigging_data: dict[str, Any]) -> list[RiggingIssue]:
        """检查约束设置"""
        issues = []

        constraints = rigging_data.get("constraints", [])

        for constraint in constraints:
            constraint_type = constraint.get("type", "unknown")
            target = constraint.get("target")

            # 检查约束目标
            if not target:
                issues.append(RiggingIssue(
                    issue_type=RiggingIssueType.CONSTRAINT_SETUP,
                    severity=SeverityLevel.HIGH,
                    bone_name=constraint.get("bone", "unknown"),
                    description=f"Constraint {constraint_type} has no target",
                    suggested_fix="Set a valid target for the constraint"
                ))

        return issues

    def _check_ik_fk_setup(self, rigging_data: dict[str, Any]) -> list[RiggingIssue]:
        """检查IK/FK设置"""
        issues = []

        ik_chains = rigging_data.get("ik_chains", [])

        for ik_chain in ik_chains:
            chain_length = len(ik_chain.get("bones", []))
            min_length, max_length = self.bone_standards["ideal_ik_chain_length"]

            if chain_length < min_length or chain_length > max_length:
                issues.append(RiggingIssue(
                    issue_type=RiggingIssueType.IK_FK_SETUP,
                    severity=SeverityLevel.MEDIUM,
                    bone_name=ik_chain.get("name", "unknown"),
                    description=f"IK chain length {chain_length} not optimal",
                    suggested_fix=f"Adjust IK chain to {min_length}-{max_length} bones"
                ))

        return issues

    def _check_deformation_quality(self, rigging_data: dict[str, Any]) -> list[RiggingIssue]:
        """检查变形质量"""
        issues = []

        # 这里可以添加更复杂的变形质量检查
        # 例如检查关节弯曲时的体积保持等

        return issues

    def _check_performance_impact(self, bones: list[BoneInfo], rigging_data: dict[str, Any]) -> list[RiggingIssue]:
        """检查性能影响"""
        issues = []

        # 检查骨骼总数
        if len(bones) > self.bone_standards["max_bones"]:
            issues.append(RiggingIssue(
                issue_type=RiggingIssueType.PERFORMANCE,
                severity=SeverityLevel.HIGH,
                bone_name="rig",
                description=f"Too many bones: {len(bones)}",
                suggested_fix="Reduce bone count for better performance",
                performance_impact=0.8
            ))

        return issues

    # 辅助方法
    def _calculate_hierarchy_depth(self, bones: list[BoneInfo]) -> int:
        """计算层级深度"""
        # 简化实现
        return 5

    def _calculate_weight_quality_score(self, rigging_data: dict[str, Any]) -> float:
        """计算权重质量评分"""
        # 简化实现
        return 0.8

    def _calculate_hierarchy_score(self, bones: list[BoneInfo]) -> float:
        """计算层级评分"""
        # 简化实现
        return 0.7

    def _calculate_naming_score(self, bones: list[BoneInfo]) -> float:
        """计算命名评分"""
        # 简化实现
        return 0.6

    def _calculate_performance_score(self, bones: list[BoneInfo], rigging_data: dict[str, Any]) -> float:
        """计算性能评分"""
        # 简化实现
        return 0.8

    def _is_side_bone(self, bone_name: str) -> bool:
        """检查是否为侧边骨骼"""
        name_lower = bone_name.lower()
        left_prefixes = self.naming_conventions["prefixes"]["left"]
        right_prefixes = self.naming_conventions["prefixes"]["right"]

        return any(name_lower.startswith(prefix.lower()) for prefix in left_prefixes + right_prefixes)

    def _get_opposite_bone_name(self, bone_name: str) -> str:
        """获取对应侧骨骼名称"""
        name_lower = bone_name.lower()

        # 检查左侧前缀
        for prefix in self.naming_conventions["prefixes"]["left"]:
            if name_lower.startswith(prefix.lower()):
                # 替换为右侧前缀
                right_prefix = self.naming_conventions["prefixes"]["right"][0]
                return right_prefix + bone_name[len(prefix):]

        # 检查右侧前缀
        for prefix in self.naming_conventions["prefixes"]["right"]:
            if name_lower.startswith(prefix.lower()):
                # 替换为左侧前缀
                left_prefix = self.naming_conventions["prefixes"]["left"][0]
                return left_prefix + bone_name[len(prefix):]

        return bone_name

    def _calculate_overall_score(self, metrics: RiggingMetrics, issues: list[RiggingIssue]) -> float:
        """计算总体评分"""
        base_score = metrics.overall_score

        # 根据问题严重程度扣分
        penalty = 0.0
        for issue in issues:
            if issue.severity == SeverityLevel.CRITICAL:
                penalty += 0.2
            elif issue.severity == SeverityLevel.HIGH:
                penalty += 0.1
            elif issue.severity == SeverityLevel.MEDIUM:
                penalty += 0.05
            elif issue.severity == SeverityLevel.LOW:
                penalty += 0.02

        return max(0.0, base_score - penalty)

    def _generate_rigging_recommendations(self, metrics: RiggingMetrics, issues: list[RiggingIssue]) -> list[str]:
        """生成绑定建议"""
        recommendations = []

        # 基于指标的建议
        if metrics.weight_quality_score < 0.7:
            recommendations.append("Improve weight painting quality")

        if metrics.hierarchy_score < 0.7:
            recommendations.append("Optimize bone hierarchy structure")

        if metrics.naming_score < 0.7:
            recommendations.append("Follow standard naming conventions")

        if metrics.performance_score < 0.7:
            recommendations.append("Optimize rig for better performance")

        # 基于问题的建议
        critical_issues = [issue for issue in issues if issue.severity == SeverityLevel.CRITICAL]
        if critical_issues:
            recommendations.append("Fix critical rigging issues immediately")

        return recommendations

    def _analyze_bone_structure(self, bones: list[BoneInfo]) -> dict[str, Any]:
        """分析骨骼结构"""
        return {
            "total_bones": len(bones),
            "deform_bones": sum(1 for bone in bones if bone.is_deform_bone),
            "average_bone_length": np.mean([bone.length for bone in bones]) if bones else 0,
            "bones_with_constraints": sum(1 for bone in bones if bone.has_constraints)
        }

    def _analyze_weight_distribution(self, rigging_data: dict[str, Any]) -> dict[str, Any]:
        """分析权重分布"""
        weight_data = rigging_data.get("weights", {})

        if not weight_data:
            return {"status": "no_weight_data"}

        total_vertices = len(weight_data)
        avg_influences = np.mean([len(weights) for weights in weight_data.values()]) if weight_data else 0

        return {
            "total_vertices": total_vertices,
            "average_influences_per_vertex": avg_influences,
            "weight_distribution": "analyzed"
        }

    def _analyze_performance_impact(self, bones: list[BoneInfo], rigging_data: dict[str, Any]) -> dict[str, Any]:
        """分析性能影响"""
        return {
            "bone_count_impact": "low" if len(bones) < 100 else "high",
            "constraint_impact": "medium",
            "estimated_performance": "good"
        }
