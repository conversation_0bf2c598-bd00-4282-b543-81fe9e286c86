"""
Mixamo专业动画库集成
Mixamo Professional Animation Library Integration
"""

try:
    import aiohttp
    AIOHTTP_AVAILABLE = True
except ImportError:
    AIOHTTP_AVAILABLE = False
    aiohttp = None
import os
from dataclasses import dataclass
from typing import Any

from loguru import logger


@dataclass
class MixamoCharacter:
    """Mixamo角色"""
    id: str
    name: str
    type: str  # "character", "skeleton"
    thumbnail_url: str
    download_url: str
    bone_count: int
    file_size: int


@dataclass
class MixamoAnimation:
    """Mixamo动画"""
    id: str
    name: str
    category: str
    duration: float
    frame_rate: int
    thumbnail_url: str
    preview_url: str
    download_url: str
    tags: list[str]
    complexity: str  # "simple", "medium", "complex"


class MixamoClient:
    """Mixamo客户端"""

    def __init__(self, api_key: str | None = None):
        self.api_key = api_key or os.getenv("MIXAMO_API_KEY")
        self.base_url = "https://www.mixamo.com/api/v1"
        self.session = None
        self.characters_cache = {}
        self.animations_cache = {}

    async def __aenter__(self):
        """异步上下文管理器入口"""
        if AIOHTTP_AVAILABLE:
            self.session = aiohttp.ClientSession()
        else:
            logger.warning("aiohttp not available, Mixamo integration disabled")
            self.session = None
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()

    async def authenticate(self) -> bool:
        """认证"""
        if not AIOHTTP_AVAILABLE:
            logger.warning("aiohttp not available, cannot authenticate with Mixamo")
            return False

        if not self.api_key:
            logger.warning("No Mixamo API key provided")
            return False

        try:
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }

            async with self.session.get(f"{self.base_url}/auth/verify", headers=headers) as response:
                if response.status == 200:
                    logger.success("Mixamo authentication successful")
                    return True
                else:
                    logger.error(f"Mixamo authentication failed: {response.status}")
                    return False

        except Exception as e:
            logger.error(f"Mixamo authentication error: {e}")
            return False

    async def get_characters(self, limit: int = 50) -> list[MixamoCharacter]:
        """获取角色列表"""
        if not self.session:
            raise RuntimeError("Session not initialized")

        try:
            headers = {"Authorization": f"Bearer {self.api_key}"}
            params = {"limit": limit, "type": "character"}

            async with self.session.get(
                f"{self.base_url}/characters",
                headers=headers,
                params=params
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    characters = []

                    for char_data in data.get("results", []):
                        character = MixamoCharacter(
                            id=char_data["id"],
                            name=char_data["name"],
                            type=char_data.get("type", "character"),
                            thumbnail_url=char_data.get("thumbnail", ""),
                            download_url=char_data.get("download_url", ""),
                            bone_count=char_data.get("bone_count", 0),
                            file_size=char_data.get("file_size", 0)
                        )
                        characters.append(character)

                    self.characters_cache = {char.id: char for char in characters}
                    logger.info(f"Retrieved {len(characters)} characters from Mixamo")
                    return characters
                else:
                    logger.error(f"Failed to get characters: {response.status}")
                    return []

        except Exception as e:
            logger.error(f"Error getting characters: {e}")
            return []

    async def get_animations(self, category: str | None = None, limit: int = 100) -> list[MixamoAnimation]:
        """获取动画列表"""
        if not self.session:
            raise RuntimeError("Session not initialized")

        try:
            headers = {"Authorization": f"Bearer {self.api_key}"}
            params = {"limit": limit}
            if category:
                params["category"] = category

            async with self.session.get(
                f"{self.base_url}/animations",
                headers=headers,
                params=params
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    animations = []

                    for anim_data in data.get("results", []):
                        animation = MixamoAnimation(
                            id=anim_data["id"],
                            name=anim_data["name"],
                            category=anim_data.get("category", "general"),
                            duration=anim_data.get("duration", 0.0),
                            frame_rate=anim_data.get("frame_rate", 30),
                            thumbnail_url=anim_data.get("thumbnail", ""),
                            preview_url=anim_data.get("preview_url", ""),
                            download_url=anim_data.get("download_url", ""),
                            tags=anim_data.get("tags", []),
                            complexity=anim_data.get("complexity", "medium")
                        )
                        animations.append(animation)

                    # 更新缓存
                    for anim in animations:
                        self.animations_cache[anim.id] = anim

                    logger.info(f"Retrieved {len(animations)} animations from Mixamo")
                    return animations
                else:
                    logger.error(f"Failed to get animations: {response.status}")
                    return []

        except Exception as e:
            logger.error(f"Error getting animations: {e}")
            return []

    async def search_animations(self, query: str, category: str | None = None) -> list[MixamoAnimation]:
        """搜索动画"""
        try:
            headers = {"Authorization": f"Bearer {self.api_key}"}
            params = {"q": query, "limit": 50}
            if category:
                params["category"] = category

            async with self.session.get(
                f"{self.base_url}/animations/search",
                headers=headers,
                params=params
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    animations = []

                    for anim_data in data.get("results", []):
                        animation = MixamoAnimation(
                            id=anim_data["id"],
                            name=anim_data["name"],
                            category=anim_data.get("category", "general"),
                            duration=anim_data.get("duration", 0.0),
                            frame_rate=anim_data.get("frame_rate", 30),
                            thumbnail_url=anim_data.get("thumbnail", ""),
                            preview_url=anim_data.get("preview_url", ""),
                            download_url=anim_data.get("download_url", ""),
                            tags=anim_data.get("tags", []),
                            complexity=anim_data.get("complexity", "medium")
                        )
                        animations.append(animation)

                    logger.info(f"Found {len(animations)} animations for query: {query}")
                    return animations
                else:
                    logger.error(f"Search failed: {response.status}")
                    return []

        except Exception as e:
            logger.error(f"Error searching animations: {e}")
            return []

    async def download_animation(self, animation_id: str, character_id: str,
                               output_path: str, format: str = "fbx") -> bool:
        """下载动画"""
        try:
            headers = {"Authorization": f"Bearer {self.api_key}"}
            data = {
                "animation_id": animation_id,
                "character_id": character_id,
                "format": format,
                "frame_rate": 30,
                "skin": True,
                "reduce_keyframes": False
            }

            # 请求下载
            async with self.session.post(
                f"{self.base_url}/animations/download",
                headers=headers,
                json=data
            ) as response:
                if response.status == 200:
                    download_data = await response.json()
                    download_url = download_data.get("download_url")

                    if download_url:
                        # 下载文件
                        async with self.session.get(download_url) as file_response:
                            if file_response.status == 200:
                                os.makedirs(os.path.dirname(output_path), exist_ok=True)
                                with open(output_path, 'wb') as f:
                                    async for chunk in file_response.content.iter_chunked(8192):
                                        f.write(chunk)

                                logger.success(f"Animation downloaded: {output_path}")
                                return True
                            else:
                                logger.error(f"Failed to download file: {file_response.status}")
                                return False
                    else:
                        logger.error("No download URL provided")
                        return False
                else:
                    logger.error(f"Download request failed: {response.status}")
                    return False

        except Exception as e:
            logger.error(f"Error downloading animation: {e}")
            return False


class MixamoIntegration:
    """Mixamo集成服务"""

    def __init__(self):
        self.client = None
        self.local_cache_dir = "cache/mixamo"
        self.animation_mapping = self._create_animation_mapping()

    def _create_animation_mapping(self) -> dict[str, list[str]]:
        """创建动画映射"""
        return {
            "walk": ["Walking", "Walk Forward", "Walk Backward", "Casual Walk"],
            "run": ["Running", "Sprint", "Jog", "Fast Run"],
            "jump": ["Jumping", "Jump Up", "Jump Forward", "Leap"],
            "attack": ["Punch", "Kick", "Sword Slash", "Fighting"],
            "dance": ["Dancing", "Hip Hop Dance", "Salsa", "Ballet"],
            "idle": ["Idle", "Standing Idle", "Breathing Idle", "Relaxed Idle"],
            "combat": ["Fighting", "Boxing", "Martial Arts", "Combat Idle"],
            "acrobatic": ["Backflip", "Cartwheel", "Handstand", "Parkour"],
            "gesture": ["Wave", "Point", "Thumbs Up", "Clap"],
            "expression": ["Happy", "Sad", "Angry", "Surprised"]
        }

    async def initialize(self, api_key: str | None = None) -> bool:
        """初始化Mixamo集成"""
        try:
            self.client = MixamoClient(api_key)
            async with self.client:
                success = await self.client.authenticate()
                if success:
                    logger.success("Mixamo integration initialized")
                    return True
                else:
                    logger.error("Failed to initialize Mixamo integration")
                    return False
        except Exception as e:
            logger.error(f"Error initializing Mixamo: {e}")
            return False

    async def find_matching_animations(self, action_type: str, limit: int = 5) -> list[MixamoAnimation]:
        """查找匹配的动画"""
        if not self.client:
            logger.warning("Mixamo client not initialized")
            return []

        try:
            # 获取映射的搜索词
            search_terms = self.animation_mapping.get(action_type.lower(), [action_type])

            all_animations = []
            async with self.client:
                for term in search_terms:
                    animations = await self.client.search_animations(term)
                    all_animations.extend(animations)

                    if len(all_animations) >= limit:
                        break

            # 去重并限制数量
            unique_animations = {}
            for anim in all_animations:
                if anim.id not in unique_animations:
                    unique_animations[anim.id] = anim

            result = list(unique_animations.values())[:limit]
            logger.info(f"Found {len(result)} matching animations for '{action_type}'")
            return result

        except Exception as e:
            logger.error(f"Error finding matching animations: {e}")
            return []

    async def get_professional_animation(self, action_type: str, character_id: str = "default") -> str | None:
        """获取专业动画文件"""
        try:
            # 查找匹配的动画
            animations = await self.find_matching_animations(action_type, limit=1)

            if not animations:
                logger.warning(f"No animations found for action: {action_type}")
                return None

            animation = animations[0]

            # 设置输出路径
            output_filename = f"{action_type}_{animation.id}.fbx"
            output_path = os.path.join(self.local_cache_dir, output_filename)

            # 检查缓存
            if os.path.exists(output_path):
                logger.info(f"Using cached animation: {output_path}")
                return output_path

            # 下载动画
            async with self.client:
                success = await self.client.download_animation(
                    animation.id, character_id, output_path
                )

                if success:
                    logger.success(f"Downloaded professional animation: {output_path}")
                    return output_path
                else:
                    logger.error(f"Failed to download animation: {animation.name}")
                    return None

        except Exception as e:
            logger.error(f"Error getting professional animation: {e}")
            return None

    async def enhance_animation_sequence(self, actions: list[dict[str, Any]]) -> list[dict[str, Any]]:
        """使用Mixamo增强动画序列"""
        enhanced_actions = []

        for action in actions:
            action_type = action.get("type", "idle")

            # 查找专业动画
            professional_file = await self.get_professional_animation(action_type)

            enhanced_action = action.copy()
            if professional_file:
                enhanced_action["mixamo_file"] = professional_file
                enhanced_action["quality"] = "professional"
                enhanced_action["source"] = "mixamo"
            else:
                enhanced_action["quality"] = "generated"
                enhanced_action["source"] = "blender"

            enhanced_actions.append(enhanced_action)

        logger.info(f"Enhanced {len(enhanced_actions)} actions with Mixamo integration")
        return enhanced_actions

    def get_available_categories(self) -> list[str]:
        """获取可用的动画分类"""
        return list(self.animation_mapping.keys())

    def get_animation_suggestions(self, text: str) -> list[str]:
        """基于文本获取动画建议"""
        suggestions = []
        text_lower = text.lower()

        for action_type, keywords in self.animation_mapping.items():
            for keyword in keywords:
                if keyword.lower() in text_lower:
                    suggestions.append(action_type)
                    break

        return list(set(suggestions))  # 去重

    async def get_animation_preview(self, action_type: str) -> str | None:
        """获取动画预览URL"""
        try:
            animations = await self.find_matching_animations(action_type, limit=1)
            if animations:
                return animations[0].preview_url
            return None
        except Exception as e:
            logger.error(f"Error getting animation preview: {e}")
            return None

    def cleanup_cache(self, max_age_days: int = 7):
        """清理缓存文件"""
        try:
            import time
            current_time = time.time()
            max_age_seconds = max_age_days * 24 * 60 * 60

            if os.path.exists(self.local_cache_dir):
                for filename in os.listdir(self.local_cache_dir):
                    file_path = os.path.join(self.local_cache_dir, filename)
                    if os.path.isfile(file_path):
                        file_age = current_time - os.path.getmtime(file_path)
                        if file_age > max_age_seconds:
                            os.remove(file_path)
                            logger.info(f"Removed old cache file: {filename}")

        except Exception as e:
            logger.error(f"Error cleaning cache: {e}")


# 全局Mixamo集成实例
mixamo_integration = MixamoIntegration()


async def get_mixamo_integration() -> MixamoIntegration:
    """获取Mixamo集成实例"""
    return mixamo_integration
