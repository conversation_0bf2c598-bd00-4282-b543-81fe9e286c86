"""
扩展动画预设库
Extended Animation Presets Library with Professional Standards
"""

from dataclasses import asdict, dataclass
from enum import Enum
from typing import Any


class AnimationCategory(Enum):
    """动画分类"""
    LOCOMOTION = "locomotion"
    COMBAT = "combat"
    ACROBATIC = "acrobatic"
    GESTURES = "gestures"
    EXPRESSIONS = "expressions"
    IDLE = "idle"
    INTERACTION = "interaction"
    CINEMATIC = "cinematic"
    DANCE = "dance"
    SPORTS = "sports"


class DifficultyLevel(Enum):
    """难度等级"""
    BEGINNER = "beginner"
    INTERMEDIATE = "intermediate"
    ADVANCED = "advanced"
    EXPERT = "expert"


@dataclass
class AnimationPreset:
    """动画预设"""
    name: str
    category: AnimationCategory
    difficulty: DifficultyLevel
    duration: float
    description: str
    keywords: list[str]
    frame_rate: int = 30
    requires_props: bool = False
    requires_partner: bool = False
    bone_requirements: list[str] = None
    technical_notes: str = ""
    reference_video: str = ""

    def __post_init__(self):
        if self.bone_requirements is None:
            self.bone_requirements = ["spine", "arms", "legs"]


class AnimationPresetsLibrary:
    """动画预设库"""

    def __init__(self):
        self.presets = self._initialize_presets()
        self.categories = self._organize_by_category()

    def _initialize_presets(self) -> dict[str, AnimationPreset]:
        """初始化预设库"""
        presets = {}

        # 基础移动动画
        locomotion_presets = [
            AnimationPreset(
                name="walk_forward",
                category=AnimationCategory.LOCOMOTION,
                difficulty=DifficultyLevel.BEGINNER,
                duration=2.0,
                description="标准向前行走",
                keywords=["walk", "forward", "步行", "向前"],
                bone_requirements=["spine", "legs", "arms"]
            ),
            AnimationPreset(
                name="walk_backward",
                category=AnimationCategory.LOCOMOTION,
                difficulty=DifficultyLevel.BEGINNER,
                duration=2.5,
                description="向后行走",
                keywords=["walk", "backward", "步行", "向后"],
                bone_requirements=["spine", "legs", "arms"]
            ),
            AnimationPreset(
                name="run_sprint",
                category=AnimationCategory.LOCOMOTION,
                difficulty=DifficultyLevel.INTERMEDIATE,
                duration=1.5,
                description="快速冲刺跑",
                keywords=["run", "sprint", "fast", "跑步", "冲刺"],
                bone_requirements=["spine", "legs", "arms"]
            ),
            AnimationPreset(
                name="jog_casual",
                category=AnimationCategory.LOCOMOTION,
                difficulty=DifficultyLevel.BEGINNER,
                duration=2.0,
                description="休闲慢跑",
                keywords=["jog", "casual", "慢跑", "休闲"],
                bone_requirements=["spine", "legs", "arms"]
            ),
            AnimationPreset(
                name="walk_sneak",
                category=AnimationCategory.LOCOMOTION,
                difficulty=DifficultyLevel.INTERMEDIATE,
                duration=3.0,
                description="潜行行走",
                keywords=["sneak", "stealth", "潜行", "隐蔽"],
                bone_requirements=["spine", "legs", "arms"]
            ),
            AnimationPreset(
                name="strafe_left",
                category=AnimationCategory.LOCOMOTION,
                difficulty=DifficultyLevel.INTERMEDIATE,
                duration=2.0,
                description="左侧移动",
                keywords=["strafe", "left", "side", "侧移", "左"],
                bone_requirements=["spine", "legs", "arms"]
            ),
            AnimationPreset(
                name="strafe_right",
                category=AnimationCategory.LOCOMOTION,
                difficulty=DifficultyLevel.INTERMEDIATE,
                duration=2.0,
                description="右侧移动",
                keywords=["strafe", "right", "side", "侧移", "右"],
                bone_requirements=["spine", "legs", "arms"]
            )
        ]

        # 战斗动画
        combat_presets = [
            AnimationPreset(
                name="punch_jab",
                category=AnimationCategory.COMBAT,
                difficulty=DifficultyLevel.BEGINNER,
                duration=0.8,
                description="快速直拳",
                keywords=["punch", "jab", "拳击", "直拳"],
                bone_requirements=["spine", "arms", "shoulders"]
            ),
            AnimationPreset(
                name="punch_hook",
                category=AnimationCategory.COMBAT,
                difficulty=DifficultyLevel.INTERMEDIATE,
                duration=1.2,
                description="勾拳攻击",
                keywords=["punch", "hook", "拳击", "勾拳"],
                bone_requirements=["spine", "arms", "shoulders", "torso"]
            ),
            AnimationPreset(
                name="kick_front",
                category=AnimationCategory.COMBAT,
                difficulty=DifficultyLevel.INTERMEDIATE,
                duration=1.5,
                description="正踢",
                keywords=["kick", "front", "踢腿", "正踢"],
                bone_requirements=["spine", "legs", "hips"]
            ),
            AnimationPreset(
                name="kick_roundhouse",
                category=AnimationCategory.COMBAT,
                difficulty=DifficultyLevel.ADVANCED,
                duration=2.0,
                description="回旋踢",
                keywords=["kick", "roundhouse", "踢腿", "回旋"],
                bone_requirements=["spine", "legs", "hips", "torso"]
            ),
            AnimationPreset(
                name="block_high",
                category=AnimationCategory.COMBAT,
                difficulty=DifficultyLevel.BEGINNER,
                duration=1.0,
                description="高位格挡",
                keywords=["block", "defend", "high", "格挡", "防御"],
                bone_requirements=["arms", "shoulders"]
            ),
            AnimationPreset(
                name="dodge_roll",
                category=AnimationCategory.COMBAT,
                difficulty=DifficultyLevel.ADVANCED,
                duration=1.8,
                description="翻滚闪避",
                keywords=["dodge", "roll", "闪避", "翻滚"],
                bone_requirements=["spine", "legs", "arms", "full_body"]
            ),
            AnimationPreset(
                name="sword_slash",
                category=AnimationCategory.COMBAT,
                difficulty=DifficultyLevel.INTERMEDIATE,
                duration=1.5,
                description="剑斩攻击",
                keywords=["sword", "slash", "剑", "斩击"],
                bone_requirements=["arms", "shoulders", "torso"],
                requires_props=True
            )
        ]

        # 特技动画
        acrobatic_presets = [
            AnimationPreset(
                name="backflip",
                category=AnimationCategory.ACROBATIC,
                difficulty=DifficultyLevel.ADVANCED,
                duration=2.5,
                description="后空翻",
                keywords=["backflip", "flip", "后空翻", "翻转"],
                bone_requirements=["full_body"]
            ),
            AnimationPreset(
                name="frontflip",
                category=AnimationCategory.ACROBATIC,
                difficulty=DifficultyLevel.ADVANCED,
                duration=2.2,
                description="前空翻",
                keywords=["frontflip", "flip", "前空翻", "翻转"],
                bone_requirements=["full_body"]
            ),
            AnimationPreset(
                name="cartwheel",
                category=AnimationCategory.ACROBATIC,
                difficulty=DifficultyLevel.INTERMEDIATE,
                duration=2.0,
                description="侧手翻",
                keywords=["cartwheel", "侧手翻", "翻转"],
                bone_requirements=["full_body"]
            ),
            AnimationPreset(
                name="handstand",
                category=AnimationCategory.ACROBATIC,
                difficulty=DifficultyLevel.ADVANCED,
                duration=3.0,
                description="倒立",
                keywords=["handstand", "倒立", "平衡"],
                bone_requirements=["arms", "shoulders", "core"]
            ),
            AnimationPreset(
                name="wall_run",
                category=AnimationCategory.ACROBATIC,
                difficulty=DifficultyLevel.EXPERT,
                duration=2.5,
                description="跑墙",
                keywords=["wall", "run", "parkour", "跑墙", "跑酷"],
                bone_requirements=["full_body"]
            )
        ]

        # 手势动画
        gesture_presets = [
            AnimationPreset(
                name="wave_hello",
                category=AnimationCategory.GESTURES,
                difficulty=DifficultyLevel.BEGINNER,
                duration=2.0,
                description="挥手打招呼",
                keywords=["wave", "hello", "greeting", "挥手", "打招呼"],
                bone_requirements=["arms", "hands"]
            ),
            AnimationPreset(
                name="point_forward",
                category=AnimationCategory.GESTURES,
                difficulty=DifficultyLevel.BEGINNER,
                duration=1.5,
                description="向前指",
                keywords=["point", "forward", "指", "向前"],
                bone_requirements=["arms", "hands"]
            ),
            AnimationPreset(
                name="thumbs_up",
                category=AnimationCategory.GESTURES,
                difficulty=DifficultyLevel.BEGINNER,
                duration=1.0,
                description="竖拇指",
                keywords=["thumbs", "up", "good", "竖拇指", "赞"],
                bone_requirements=["arms", "hands", "fingers"]
            ),
            AnimationPreset(
                name="clap_hands",
                category=AnimationCategory.GESTURES,
                difficulty=DifficultyLevel.BEGINNER,
                duration=2.0,
                description="鼓掌",
                keywords=["clap", "applause", "鼓掌", "拍手"],
                bone_requirements=["arms", "hands"]
            ),
            AnimationPreset(
                name="salute_military",
                category=AnimationCategory.GESTURES,
                difficulty=DifficultyLevel.BEGINNER,
                duration=1.5,
                description="军礼",
                keywords=["salute", "military", "敬礼", "军礼"],
                bone_requirements=["arms", "hands"]
            )
        ]

        # 表情动画
        expression_presets = [
            AnimationPreset(
                name="smile_happy",
                category=AnimationCategory.EXPRESSIONS,
                difficulty=DifficultyLevel.BEGINNER,
                duration=2.0,
                description="开心微笑",
                keywords=["smile", "happy", "微笑", "开心"],
                bone_requirements=["face", "eyes", "mouth"]
            ),
            AnimationPreset(
                name="frown_sad",
                category=AnimationCategory.EXPRESSIONS,
                difficulty=DifficultyLevel.BEGINNER,
                duration=2.0,
                description="皱眉难过",
                keywords=["frown", "sad", "皱眉", "难过"],
                bone_requirements=["face", "eyes", "mouth"]
            ),
            AnimationPreset(
                name="surprise_shock",
                category=AnimationCategory.EXPRESSIONS,
                difficulty=DifficultyLevel.BEGINNER,
                duration=1.5,
                description="惊讶震惊",
                keywords=["surprise", "shock", "惊讶", "震惊"],
                bone_requirements=["face", "eyes", "mouth", "eyebrows"]
            ),
            AnimationPreset(
                name="anger_rage",
                category=AnimationCategory.EXPRESSIONS,
                difficulty=DifficultyLevel.INTERMEDIATE,
                duration=2.5,
                description="愤怒暴怒",
                keywords=["anger", "rage", "愤怒", "暴怒"],
                bone_requirements=["face", "eyes", "mouth", "eyebrows"]
            )
        ]

        # 待机动画
        idle_presets = [
            AnimationPreset(
                name="idle_breathing",
                category=AnimationCategory.IDLE,
                difficulty=DifficultyLevel.BEGINNER,
                duration=4.0,
                description="呼吸待机",
                keywords=["idle", "breathing", "待机", "呼吸"],
                bone_requirements=["spine", "chest"]
            ),
            AnimationPreset(
                name="idle_looking_around",
                category=AnimationCategory.IDLE,
                difficulty=DifficultyLevel.BEGINNER,
                duration=5.0,
                description="环顾四周",
                keywords=["idle", "look", "around", "待机", "环顾"],
                bone_requirements=["head", "neck", "eyes"]
            ),
            AnimationPreset(
                name="idle_fidget",
                category=AnimationCategory.IDLE,
                difficulty=DifficultyLevel.BEGINNER,
                duration=3.0,
                description="小动作",
                keywords=["idle", "fidget", "待机", "小动作"],
                bone_requirements=["arms", "hands"]
            ),
            AnimationPreset(
                name="idle_stretch",
                category=AnimationCategory.IDLE,
                difficulty=DifficultyLevel.BEGINNER,
                duration=4.0,
                description="伸展身体",
                keywords=["idle", "stretch", "待机", "伸展"],
                bone_requirements=["arms", "spine", "legs"]
            )
        ]

        # 互动动画
        interaction_presets = [
            AnimationPreset(
                name="sit_down",
                category=AnimationCategory.INTERACTION,
                difficulty=DifficultyLevel.INTERMEDIATE,
                duration=2.5,
                description="坐下",
                keywords=["sit", "down", "坐下", "坐"],
                bone_requirements=["legs", "spine", "hips"]
            ),
            AnimationPreset(
                name="stand_up",
                category=AnimationCategory.INTERACTION,
                difficulty=DifficultyLevel.INTERMEDIATE,
                duration=2.0,
                description="站起",
                keywords=["stand", "up", "站起", "起立"],
                bone_requirements=["legs", "spine", "hips"]
            ),
            AnimationPreset(
                name="pick_up_object",
                category=AnimationCategory.INTERACTION,
                difficulty=DifficultyLevel.INTERMEDIATE,
                duration=2.5,
                description="拾取物品",
                keywords=["pick", "up", "object", "拾取", "捡起"],
                bone_requirements=["arms", "hands", "spine"]
            ),
            AnimationPreset(
                name="open_door",
                category=AnimationCategory.INTERACTION,
                difficulty=DifficultyLevel.BEGINNER,
                duration=2.0,
                description="开门",
                keywords=["open", "door", "开门", "门"],
                bone_requirements=["arms", "hands"]
            )
        ]

        # 电影级动画
        cinematic_presets = [
            AnimationPreset(
                name="dramatic_fall",
                category=AnimationCategory.CINEMATIC,
                difficulty=DifficultyLevel.ADVANCED,
                duration=3.5,
                description="戏剧性倒下",
                keywords=["dramatic", "fall", "戏剧", "倒下"],
                bone_requirements=["full_body"]
            ),
            AnimationPreset(
                name="heroic_pose",
                category=AnimationCategory.CINEMATIC,
                difficulty=DifficultyLevel.INTERMEDIATE,
                duration=3.0,
                description="英雄姿势",
                keywords=["heroic", "pose", "英雄", "姿势"],
                bone_requirements=["full_body"]
            ),
            AnimationPreset(
                name="victory_celebration",
                category=AnimationCategory.CINEMATIC,
                difficulty=DifficultyLevel.INTERMEDIATE,
                duration=4.0,
                description="胜利庆祝",
                keywords=["victory", "celebration", "胜利", "庆祝"],
                bone_requirements=["arms", "legs", "spine"]
            )
        ]

        # 舞蹈动画
        dance_presets = [
            AnimationPreset(
                name="dance_basic_step",
                category=AnimationCategory.DANCE,
                difficulty=DifficultyLevel.INTERMEDIATE,
                duration=4.0,
                description="基础舞步",
                keywords=["dance", "basic", "step", "舞蹈", "基础"],
                bone_requirements=["legs", "arms", "spine"]
            ),
            AnimationPreset(
                name="dance_spin",
                category=AnimationCategory.DANCE,
                difficulty=DifficultyLevel.ADVANCED,
                duration=2.5,
                description="旋转舞蹈",
                keywords=["dance", "spin", "rotate", "舞蹈", "旋转"],
                bone_requirements=["full_body"]
            )
        ]

        # 运动动画
        sports_presets = [
            AnimationPreset(
                name="basketball_shoot",
                category=AnimationCategory.SPORTS,
                difficulty=DifficultyLevel.INTERMEDIATE,
                duration=2.0,
                description="篮球投篮",
                keywords=["basketball", "shoot", "篮球", "投篮"],
                bone_requirements=["arms", "legs", "spine"],
                requires_props=True
            ),
            AnimationPreset(
                name="soccer_kick",
                category=AnimationCategory.SPORTS,
                difficulty=DifficultyLevel.INTERMEDIATE,
                duration=1.8,
                description="足球踢球",
                keywords=["soccer", "kick", "football", "足球", "踢球"],
                bone_requirements=["legs", "hips", "spine"]
            ),
            AnimationPreset(
                name="tennis_serve",
                category=AnimationCategory.SPORTS,
                difficulty=DifficultyLevel.ADVANCED,
                duration=2.5,
                description="网球发球",
                keywords=["tennis", "serve", "网球", "发球"],
                bone_requirements=["arms", "shoulders", "spine"],
                requires_props=True
            )
        ]

        # 合并所有预设
        all_presets = (locomotion_presets + combat_presets + acrobatic_presets +
                      gesture_presets + expression_presets + idle_presets +
                      interaction_presets + cinematic_presets + dance_presets +
                      sports_presets)

        # 转换为字典
        for preset in all_presets:
            presets[preset.name] = preset

        return presets

    def _organize_by_category(self) -> dict[str, list[str]]:
        """按分类组织预设"""
        categories = {}
        for preset_name, preset in self.presets.items():
            category = preset.category.value
            if category not in categories:
                categories[category] = []
            categories[category].append(preset_name)
        return categories

    def get_presets_by_category(self, category: str) -> list[AnimationPreset]:
        """按分类获取预设"""
        return [preset for preset in self.presets.values()
                if preset.category.value == category]

    def get_presets_by_difficulty(self, difficulty: str) -> list[AnimationPreset]:
        """按难度获取预设"""
        return [preset for preset in self.presets.values()
                if preset.difficulty.value == difficulty]

    def search_presets(self, keywords: list[str]) -> list[AnimationPreset]:
        """搜索预设"""
        results = []
        keywords_lower = [kw.lower() for kw in keywords]

        for preset in self.presets.values():
            preset_keywords = [kw.lower() for kw in preset.keywords]
            if any(kw in preset_keywords for kw in keywords_lower):
                results.append(preset)

        return results

    def get_preset(self, name: str) -> AnimationPreset | None:
        """获取特定预设"""
        return self.presets.get(name)

    def get_all_categories(self) -> list[str]:
        """获取所有分类"""
        return list(self.categories.keys())

    def get_category_presets(self, category: str) -> dict[str, Any]:
        """获取分类下的所有预设（用于API）"""
        if category not in self.categories:
            return {}

        preset_names = self.categories[category]
        return {name: self.presets[name].keywords for name in preset_names}

    def export_presets_for_api(self) -> dict[str, Any]:
        """导出预设供API使用"""
        api_presets = {}

        for category, preset_names in self.categories.items():
            api_presets[category] = []
            for name in preset_names:
                preset = self.presets[name]
                api_presets[category].extend(preset.keywords[:3])  # 只取前3个关键词

        return api_presets

    def get_preset_details(self, name: str) -> dict[str, Any]:
        """获取预设详细信息"""
        preset = self.get_preset(name)
        if not preset:
            return {}

        return asdict(preset)
