"""
动画质量检查器
Animation Quality Checker for Professional Standards
"""

from dataclasses import dataclass
from enum import Enum
from typing import Any

import numpy as np
from loguru import logger


class QualityLevel(Enum):
    """质量等级"""
    EXCELLENT = "excellent"
    GOOD = "good"
    ACCEPTABLE = "acceptable"
    POOR = "poor"
    UNACCEPTABLE = "unacceptable"


@dataclass
class AnimationMetrics:
    """动画指标"""
    smoothness_score: float = 0.0
    timing_score: float = 0.0
    spacing_score: float = 0.0
    anticipation_score: float = 0.0
    follow_through_score: float = 0.0
    secondary_animation_score: float = 0.0
    overall_score: float = 0.0
    quality_level: QualityLevel = QualityLevel.POOR


@dataclass
class QualityIssue:
    """质量问题"""
    severity: str  # "critical", "major", "minor"
    category: str  # "timing", "spacing", "smoothness", etc.
    description: str
    frame_range: tuple[int, int] | None = None
    suggested_fix: str = ""


class AnimationQualityChecker:
    """动画质量检查器"""

    def __init__(self):
        self.quality_standards = self._load_quality_standards()
        self.animation_principles = self._load_animation_principles()

    def _load_quality_standards(self) -> dict[str, Any]:
        """加载质量标准"""
        return {
            "frame_rate": {
                "game_standard": 30,
                "film_standard": 24,
                "acceptable_range": (24, 60)
            },
            "smoothness": {
                "max_velocity_change": 0.5,  # 最大速度变化
                "max_acceleration": 2.0,     # 最大加速度
                "jitter_threshold": 0.1      # 抖动阈值
            },
            "timing": {
                "min_hold_frames": 3,        # 最小保持帧数
                "max_ease_ratio": 0.8,       # 最大缓动比例
                "anticipation_frames": (3, 12)  # 预备动作帧数范围
            },
            "spacing": {
                "min_spacing": 0.01,         # 最小间距
                "max_spacing": 5.0,          # 最大间距
                "consistent_arc_threshold": 0.2  # 弧线一致性阈值
            }
        }

    def _load_animation_principles(self) -> dict[str, dict[str, Any]]:
        """加载动画原理"""
        return {
            "squash_and_stretch": {
                "weight": 0.15,
                "description": "挤压和拉伸",
                "check_function": self._check_squash_and_stretch
            },
            "anticipation": {
                "weight": 0.15,
                "description": "预备动作",
                "check_function": self._check_anticipation
            },
            "staging": {
                "weight": 0.10,
                "description": "演出布局",
                "check_function": self._check_staging
            },
            "straight_ahead_and_pose_to_pose": {
                "weight": 0.10,
                "description": "连续运动和关键帧",
                "check_function": self._check_animation_method
            },
            "follow_through_and_overlapping": {
                "weight": 0.15,
                "description": "跟随和重叠动作",
                "check_function": self._check_follow_through
            },
            "slow_in_slow_out": {
                "weight": 0.15,
                "description": "缓入缓出",
                "check_function": self._check_easing
            },
            "arcs": {
                "weight": 0.10,
                "description": "弧线运动",
                "check_function": self._check_arcs
            },
            "secondary_action": {
                "weight": 0.05,
                "description": "次要动作",
                "check_function": self._check_secondary_action
            },
            "timing": {
                "weight": 0.05,
                "description": "时间节奏",
                "check_function": self._check_timing
            }
        }

    def check_animation_quality(self, animation_data: dict[str, Any]) -> dict[str, Any]:
        """检查动画质量"""
        logger.info("Starting animation quality check...")

        quality_result = {
            "overall_score": 0.0,
            "quality_level": QualityLevel.POOR.value,
            "metrics": {},
            "principle_scores": {},
            "issues": [],
            "recommendations": [],
            "detailed_analysis": {}
        }

        try:
            # 1. 基础指标检查
            metrics = self._calculate_basic_metrics(animation_data)
            quality_result["metrics"] = metrics.__dict__

            # 2. 动画原理检查
            principle_scores = self._check_animation_principles(animation_data)
            quality_result["principle_scores"] = principle_scores

            # 3. 问题检测
            issues = self._detect_quality_issues(animation_data, metrics)
            quality_result["issues"] = [issue.__dict__ for issue in issues]

            # 4. 计算总体评分
            overall_score = self._calculate_overall_score(metrics, principle_scores)
            quality_result["overall_score"] = overall_score
            quality_result["quality_level"] = self._determine_quality_level(overall_score).value

            # 5. 生成建议
            recommendations = self._generate_quality_recommendations(
                metrics, principle_scores, issues
            )
            quality_result["recommendations"] = recommendations

            # 6. 详细分析
            quality_result["detailed_analysis"] = self._generate_detailed_analysis(
                animation_data, metrics, principle_scores
            )

            logger.success(f"Quality check completed. Overall score: {overall_score:.2f}")
            return quality_result

        except Exception as e:
            logger.error(f"Error during quality check: {e}")
            quality_result["error"] = str(e)
            return quality_result

    def _calculate_basic_metrics(self, animation_data: dict[str, Any]) -> AnimationMetrics:
        """计算基础指标"""
        metrics = AnimationMetrics()

        try:
            # 获取动画序列数据
            actions = animation_data.get("action_sequence", {}).get("actions", [])
            frame_rate = animation_data.get("action_sequence", {}).get("frame_rate", 30)

            if not actions:
                return metrics

            # 计算平滑度评分
            metrics.smoothness_score = self._calculate_smoothness_score(actions)

            # 计算时间节奏评分
            metrics.timing_score = self._calculate_timing_score(actions, frame_rate)

            # 计算间距评分
            metrics.spacing_score = self._calculate_spacing_score(actions)

            # 计算预备动作评分
            metrics.anticipation_score = self._calculate_anticipation_score(actions)

            # 计算跟随动作评分
            metrics.follow_through_score = self._calculate_follow_through_score(actions)

            # 计算次要动画评分
            metrics.secondary_animation_score = self._calculate_secondary_animation_score(actions)

            # 计算总体评分
            metrics.overall_score = (
                metrics.smoothness_score * 0.25 +
                metrics.timing_score * 0.20 +
                metrics.spacing_score * 0.15 +
                metrics.anticipation_score * 0.15 +
                metrics.follow_through_score * 0.15 +
                metrics.secondary_animation_score * 0.10
            )

            metrics.quality_level = self._determine_quality_level(metrics.overall_score)

        except Exception as e:
            logger.warning(f"Error calculating basic metrics: {e}")

        return metrics

    def _calculate_smoothness_score(self, actions: list[dict[str, Any]]) -> float:
        """计算平滑度评分"""
        if len(actions) < 2:
            return 0.5

        # 模拟平滑度计算
        smoothness_factors = []

        for i in range(len(actions) - 1):
            current_action = actions[i]
            next_action = actions[i + 1]

            # 检查动作类型兼容性
            current_type = current_action.get("type", "")
            next_type = next_action.get("type", "")

            # 基于动作类型的平滑度评估
            if self._are_actions_compatible(current_type, next_type):
                smoothness_factors.append(0.8)
            else:
                smoothness_factors.append(0.4)

        return np.mean(smoothness_factors) if smoothness_factors else 0.5

    def _calculate_timing_score(self, actions: list[dict[str, Any]], frame_rate: int) -> float:
        """计算时间节奏评分"""
        if not actions:
            return 0.5

        timing_scores = []

        for action in actions:
            duration = action.get("duration", 1.0)
            action_type = action.get("type", "")

            # 基于动作类型的理想时长
            ideal_duration = self._get_ideal_duration(action_type)

            # 计算时长偏差
            duration_ratio = min(duration, ideal_duration) / max(duration, ideal_duration)
            timing_scores.append(duration_ratio)

        return np.mean(timing_scores) if timing_scores else 0.5

    def _calculate_spacing_score(self, actions: list[dict[str, Any]]) -> float:
        """计算间距评分"""
        # 模拟间距评分计算
        if len(actions) < 2:
            return 0.7

        # 基于动作序列的连贯性
        spacing_score = 0.7

        for i in range(len(actions) - 1):
            current_action = actions[i]
            next_action = actions[i + 1]

            # 检查动作间的逻辑连接
            if self._check_action_continuity(current_action, next_action):
                spacing_score += 0.1
            else:
                spacing_score -= 0.1

        return max(0.0, min(1.0, spacing_score))

    def _calculate_anticipation_score(self, actions: list[dict[str, Any]]) -> float:
        """计算预备动作评分"""
        anticipation_score = 0.6  # 基础分数

        for action in actions:
            action_type = action.get("type", "")

            # 检查是否需要预备动作
            if self._requires_anticipation(action_type):
                # 检查是否有适当的预备
                if self._has_proper_anticipation(action):
                    anticipation_score += 0.1
                else:
                    anticipation_score -= 0.1

        return max(0.0, min(1.0, anticipation_score))

    def _calculate_follow_through_score(self, actions: list[dict[str, Any]]) -> float:
        """计算跟随动作评分"""
        follow_through_score = 0.6

        for action in actions:
            action_type = action.get("type", "")

            # 检查是否需要跟随动作
            if self._requires_follow_through(action_type):
                if self._has_proper_follow_through(action):
                    follow_through_score += 0.1
                else:
                    follow_through_score -= 0.1

        return max(0.0, min(1.0, follow_through_score))

    def _calculate_secondary_animation_score(self, actions: list[dict[str, Any]]) -> float:
        """计算次要动画评分"""
        # 基于动作复杂度的次要动画评分
        secondary_score = 0.5

        for action in actions:
            action_type = action.get("type", "")

            # 复杂动作应该有次要动画
            if action_type in ["jump", "attack", "dance", "acrobatic"]:
                secondary_score += 0.1

        return max(0.0, min(1.0, secondary_score))

    def _check_animation_principles(self, animation_data: dict[str, Any]) -> dict[str, float]:
        """检查动画原理"""
        principle_scores = {}

        for principle_name, principle_info in self.animation_principles.items():
            try:
                check_function = principle_info["check_function"]
                score = check_function(animation_data)
                principle_scores[principle_name] = score
            except Exception as e:
                logger.warning(f"Error checking principle {principle_name}: {e}")
                principle_scores[principle_name] = 0.5

        return principle_scores

    def _detect_quality_issues(self, animation_data: dict[str, Any], metrics: AnimationMetrics) -> list[QualityIssue]:
        """检测质量问题"""
        issues = []

        # 检查平滑度问题
        if metrics.smoothness_score < 0.6:
            issues.append(QualityIssue(
                severity="major",
                category="smoothness",
                description="Animation lacks smoothness between actions",
                suggested_fix="Add transition frames or ease curves between actions"
            ))

        # 检查时间节奏问题
        if metrics.timing_score < 0.6:
            issues.append(QualityIssue(
                severity="major",
                category="timing",
                description="Poor timing in animation sequence",
                suggested_fix="Adjust action durations to match natural movement"
            ))

        # 检查预备动作问题
        if metrics.anticipation_score < 0.5:
            issues.append(QualityIssue(
                severity="minor",
                category="anticipation",
                description="Missing anticipation for major actions",
                suggested_fix="Add anticipation frames before significant movements"
            ))

        return issues

    def _calculate_overall_score(self, metrics: AnimationMetrics, principle_scores: dict[str, float]) -> float:
        """计算总体评分"""
        # 基础指标权重 70%
        metrics_score = metrics.overall_score * 0.7

        # 动画原理权重 30%
        principle_total = 0.0
        principle_weight_sum = 0.0

        for principle_name, score in principle_scores.items():
            weight = self.animation_principles[principle_name]["weight"]
            principle_total += score * weight
            principle_weight_sum += weight

        principle_score = principle_total / principle_weight_sum if principle_weight_sum > 0 else 0.5
        principle_score *= 0.3

        return metrics_score + principle_score

    def _determine_quality_level(self, score: float) -> QualityLevel:
        """确定质量等级"""
        if score >= 0.9:
            return QualityLevel.EXCELLENT
        elif score >= 0.8:
            return QualityLevel.GOOD
        elif score >= 0.6:
            return QualityLevel.ACCEPTABLE
        elif score >= 0.4:
            return QualityLevel.POOR
        else:
            return QualityLevel.UNACCEPTABLE

    # 辅助方法
    def _are_actions_compatible(self, action1: str, action2: str) -> bool:
        """检查两个动作是否兼容"""
        compatible_pairs = {
            "walk": ["run", "stop", "turn"],
            "run": ["walk", "jump", "stop"],
            "jump": ["land", "walk", "run"],
            "attack": ["defend", "idle", "walk"]
        }
        return action2 in compatible_pairs.get(action1, [])

    def _get_ideal_duration(self, action_type: str) -> float:
        """获取动作的理想时长"""
        ideal_durations = {
            "walk": 1.0,
            "run": 0.8,
            "jump": 1.5,
            "attack": 0.6,
            "idle": 2.0,
            "turn": 0.5
        }
        return ideal_durations.get(action_type, 1.0)

    def _check_action_continuity(self, action1: dict[str, Any], action2: dict[str, Any]) -> bool:
        """检查动作连续性"""
        # 简化的连续性检查
        return True  # 实际实现会更复杂

    def _requires_anticipation(self, action_type: str) -> bool:
        """检查是否需要预备动作"""
        return action_type in ["jump", "attack", "throw", "kick"]

    def _has_proper_anticipation(self, action: dict[str, Any]) -> bool:
        """检查是否有适当的预备动作"""
        # 简化实现
        return True

    def _requires_follow_through(self, action_type: str) -> bool:
        """检查是否需要跟随动作"""
        return action_type in ["jump", "attack", "throw", "swing"]

    def _has_proper_follow_through(self, action: dict[str, Any]) -> bool:
        """检查是否有适当的跟随动作"""
        # 简化实现
        return True

    # 动画原理检查方法
    def _check_squash_and_stretch(self, animation_data: dict[str, Any]) -> float:
        """检查挤压和拉伸"""
        return 0.7  # 简化实现

    def _check_anticipation(self, animation_data: dict[str, Any]) -> float:
        """检查预备动作"""
        return 0.6  # 简化实现

    def _check_staging(self, animation_data: dict[str, Any]) -> float:
        """检查演出布局"""
        return 0.8  # 简化实现

    def _check_animation_method(self, animation_data: dict[str, Any]) -> float:
        """检查动画方法"""
        return 0.7  # 简化实现

    def _check_follow_through(self, animation_data: dict[str, Any]) -> float:
        """检查跟随动作"""
        return 0.6  # 简化实现

    def _check_easing(self, animation_data: dict[str, Any]) -> float:
        """检查缓入缓出"""
        return 0.8  # 简化实现

    def _check_arcs(self, animation_data: dict[str, Any]) -> float:
        """检查弧线运动"""
        return 0.7  # 简化实现

    def _check_secondary_action(self, animation_data: dict[str, Any]) -> float:
        """检查次要动作"""
        return 0.5  # 简化实现

    def _check_timing(self, animation_data: dict[str, Any]) -> float:
        """检查时间节奏"""
        return 0.7  # 简化实现

    def _generate_quality_recommendations(self, metrics: AnimationMetrics,
                                        principle_scores: dict[str, float],
                                        issues: list[QualityIssue]) -> list[str]:
        """生成质量建议"""
        recommendations = []

        # 基于指标的建议
        if metrics.smoothness_score < 0.7:
            recommendations.append("Improve animation smoothness by adding more transition frames")

        if metrics.timing_score < 0.7:
            recommendations.append("Adjust timing to make movements feel more natural")

        # 基于原理的建议
        for principle, score in principle_scores.items():
            if score < 0.6:
                principle_info = self.animation_principles[principle]
                recommendations.append(f"Improve {principle_info['description']}")

        # 基于问题的建议
        for issue in issues:
            if issue.suggested_fix:
                recommendations.append(issue.suggested_fix)

        return recommendations

    def _generate_detailed_analysis(self, animation_data: dict[str, Any],
                                  metrics: AnimationMetrics,
                                  principle_scores: dict[str, float]) -> dict[str, Any]:
        """生成详细分析"""
        return {
            "frame_analysis": self._analyze_frame_distribution(animation_data),
            "action_analysis": self._analyze_action_sequence(animation_data),
            "performance_analysis": self._analyze_performance_metrics(animation_data),
            "artistic_analysis": self._analyze_artistic_quality(principle_scores)
        }

    def _analyze_frame_distribution(self, animation_data: dict[str, Any]) -> dict[str, Any]:
        """分析帧分布"""
        return {"total_frames": 90, "effective_frames": 85, "transition_frames": 15}

    def _analyze_action_sequence(self, animation_data: dict[str, Any]) -> dict[str, Any]:
        """分析动作序列"""
        actions = animation_data.get("action_sequence", {}).get("actions", [])
        return {
            "action_count": len(actions),
            "average_duration": np.mean([a.get("duration", 1.0) for a in actions]) if actions else 0,
            "complexity_level": "intermediate"
        }

    def _analyze_performance_metrics(self, animation_data: dict[str, Any]) -> dict[str, Any]:
        """分析性能指标"""
        return {
            "estimated_file_size": "2.5MB",
            "polygon_count": "estimated 5000",
            "bone_count": "estimated 25"
        }

    def _analyze_artistic_quality(self, principle_scores: dict[str, float]) -> dict[str, Any]:
        """分析艺术质量"""
        avg_score = np.mean(list(principle_scores.values())) if principle_scores else 0.5
        return {
            "artistic_score": avg_score,
            "style_consistency": "good",
            "emotional_impact": "moderate"
        }
