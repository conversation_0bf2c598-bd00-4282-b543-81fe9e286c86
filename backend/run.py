#!/usr/bin/env python3
"""
Motion Agent Backend Runner
Motion Agent 后端运行器
"""

import subprocess
import sys


def main():
    """一键启动后端"""
    print("🚀 Motion Agent - 一键启动")
    print("=" * 30)

    try:
        # 直接启动后端，跳过所有检查
        cmd = [sys.executable, "-m", "uvicorn", "backend.app:app", "--host", "0.0.0.0", "--port", "9000", "--reload"]

        print("🌐 启动地址:")
        print("   - API: http://localhost:9000/")
        print("   - 文档: http://localhost:9000/docs")
        print("   - 健康检查: http://localhost:9000/health")
        print("\n按 Ctrl+C 停止服务器")
        print("=" * 30)

        subprocess.run(cmd)

    except KeyboardInterrupt:
        print("\n👋 服务器已停止")
    except Exception as e:
        print(f"💥 启动失败: {e}")
        print("\n💡 请先安装依赖:")
        print("   uv sync")


if __name__ == "__main__":
    main()
