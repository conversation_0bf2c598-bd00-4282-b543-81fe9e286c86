"""
对话处理任务
Conversation Processing Tasks
"""

import asyncio
import uuid
from datetime import datetime
from typing import Any

from loguru import logger

from ..models.message import MessageType
from ..models.task import TaskStatus
from . import broker


@broker.task(queue_name="conversation", max_retries=2)
async def process_conversation_message(
    conversation_id: str,
    message_id: str,
    content: str,
    message_type: str = MessageType.USER.value,
    context: dict[str, Any] = None,
    user_id: str = None,
) -> dict[str, Any]:
    """
    处理对话消息任务
    
    Args:
        conversation_id: 对话ID
        message_id: 消息ID
        content: 消息内容
        message_type: 消息类型
        context: 上下文信息
        user_id: 用户ID
    
    Returns:
        Dict[str, Any]: 处理结果
    """
    task_id = str(uuid.uuid4())
    start_time = datetime.utcnow()

    logger.info(f"Starting conversation processing task {task_id} for message {message_id}")

    try:
        # 更新任务状态
        await update_task_status(task_id, TaskStatus.RUNNING, progress=0)

        # 步骤1: 加载对话历史
        logger.info(f"Task {task_id}: Loading conversation history...")
        await update_task_status(task_id, TaskStatus.RUNNING, progress=20)

        conversation_history = await load_conversation_history(conversation_id)

        # 步骤2: 处理用户消息
        logger.info(f"Task {task_id}: Processing user message...")
        await update_task_status(task_id, TaskStatus.RUNNING, progress=40)

        processed_message = await process_user_message(content, context or {}, conversation_history)

        # 步骤3: 生成回复
        logger.info(f"Task {task_id}: Generating response...")
        await update_task_status(task_id, TaskStatus.RUNNING, progress=60)

        response = await generate_response(processed_message, conversation_history, context or {})

        # 步骤4: 保存消息和回复
        logger.info(f"Task {task_id}: Saving messages...")
        await update_task_status(task_id, TaskStatus.RUNNING, progress=80)

        saved_messages = await save_conversation_messages(
            conversation_id,
            processed_message,
            response
        )

        # 步骤5: 更新对话统计
        logger.info(f"Task {task_id}: Updating conversation stats...")
        await update_task_status(task_id, TaskStatus.RUNNING, progress=90)

        await update_conversation_stats(conversation_id, saved_messages)

        # 完成任务
        end_time = datetime.utcnow()
        duration = (end_time - start_time).total_seconds()

        result = {
            "task_id": task_id,
            "conversation_id": conversation_id,
            "message_id": message_id,
            "status": "completed",
            "processed_message": processed_message,
            "response": response,
            "saved_messages": saved_messages,
            "duration": duration,
            "completed_at": end_time.isoformat(),
        }

        await update_task_status(task_id, TaskStatus.COMPLETED, progress=100, output_data=result)

        logger.success(f"Conversation processing task {task_id} completed in {duration:.2f}s")
        return result

    except Exception as e:
        logger.exception(f"Conversation processing task {task_id} failed: {str(e)}")

        error_result = {
            "task_id": task_id,
            "conversation_id": conversation_id,
            "message_id": message_id,
            "status": "failed",
            "error": str(e),
            "error_type": type(e).__name__,
        }

        await update_task_status(
            task_id,
            TaskStatus.FAILED,
            error_message=str(e),
            error_code=type(e).__name__
        )

        raise


async def load_conversation_history(conversation_id: str) -> list[dict[str, Any]]:
    """加载对话历史"""
    # 模拟加载对话历史
    await asyncio.sleep(0.5)

    return [
        {
            "id": "msg_1",
            "content": "Hello, I want to create a walking animation",
            "message_type": "user",
            "created_at": "2024-01-01T10:00:00Z",
        },
        {
            "id": "msg_2",
            "content": "I'll help you create a walking animation. Please describe the character and walking style.",
            "message_type": "assistant",
            "created_at": "2024-01-01T10:00:05Z",
        }
    ]


async def process_user_message(
    content: str,
    context: dict[str, Any],
    history: list[dict[str, Any]]
) -> dict[str, Any]:
    """处理用户消息"""
    # 模拟消息处理
    await asyncio.sleep(1)

    return {
        "content": content,
        "processed_content": content.strip().lower(),
        "intent": "animation_request",
        "entities": {
            "action": "walk",
            "character": "default",
        },
        "confidence": 0.9,
        "context": context,
        "token_count": len(content.split()),
    }


async def generate_response(
    processed_message: dict[str, Any],
    history: list[dict[str, Any]],
    context: dict[str, Any]
) -> dict[str, Any]:
    """生成回复"""
    # 模拟回复生成
    await asyncio.sleep(2)

    intent = processed_message.get("intent", "unknown")

    if intent == "animation_request":
        response_content = "I understand you want to create an animation. I'll start generating it for you."
        action_required = "start_animation_generation"
    else:
        response_content = "I'm here to help you create animations. Please describe what you'd like to animate."
        action_required = None

    return {
        "content": response_content,
        "message_type": "assistant",
        "action_required": action_required,
        "metadata": {
            "intent": intent,
            "confidence": processed_message.get("confidence", 0.0),
            "processing_time": 2.0,
        },
        "token_count": len(response_content.split()),
    }


async def save_conversation_messages(
    conversation_id: str,
    user_message: dict[str, Any],
    assistant_response: dict[str, Any]
) -> list[dict[str, Any]]:
    """保存对话消息"""
    # 模拟保存消息
    await asyncio.sleep(0.3)

    saved_messages = [
        {
            "id": str(uuid.uuid4()),
            "conversation_id": conversation_id,
            "content": user_message["content"],
            "message_type": "user",
            "status": "completed",
            "token_count": user_message.get("token_count", 0),
            "created_at": datetime.utcnow().isoformat(),
        },
        {
            "id": str(uuid.uuid4()),
            "conversation_id": conversation_id,
            "content": assistant_response["content"],
            "message_type": "assistant",
            "status": "completed",
            "token_count": assistant_response.get("token_count", 0),
            "metadata": assistant_response.get("metadata", {}),
            "created_at": datetime.utcnow().isoformat(),
        }
    ]

    return saved_messages


async def update_conversation_stats(conversation_id: str, messages: list[dict[str, Any]]):
    """更新对话统计"""
    # 模拟更新统计
    await asyncio.sleep(0.2)

    total_tokens = sum(msg.get("token_count", 0) for msg in messages)
    message_count = len(messages)

    logger.info(f"Updated conversation {conversation_id}: +{message_count} messages, +{total_tokens} tokens")


async def update_task_status(
    task_id: str,
    status: TaskStatus,
    progress: float = None,
    output_data: dict[str, Any] = None,
    error_message: str = None,
    error_code: str = None,
):
    """更新任务状态"""
    # 这里应该更新数据库中的任务状态
    # 暂时只记录日志
    logger.info(f"Task {task_id} status updated: {status.value}, progress: {progress}%")

    if error_message:
        logger.error(f"Task {task_id} error: {error_message}")


@broker.task(queue_name="conversation", max_retries=1)
async def archive_conversation(conversation_id: str, user_id: str = None) -> dict[str, Any]:
    """归档对话任务"""
    task_id = str(uuid.uuid4())

    logger.info(f"Starting conversation archive task {task_id} for conversation {conversation_id}")

    try:
        # 模拟归档处理
        await asyncio.sleep(1)

        result = {
            "task_id": task_id,
            "conversation_id": conversation_id,
            "user_id": user_id,
            "status": "archived",
            "archived_at": datetime.utcnow().isoformat(),
        }

        logger.success(f"Conversation archive task {task_id} completed")
        return result

    except Exception as e:
        logger.exception(f"Conversation archive task {task_id} failed: {str(e)}")
        raise


@broker.task(queue_name="conversation", max_retries=1)
async def export_conversation_history(
    conversation_id: str,
    format: str = "json",
    user_id: str = None
) -> dict[str, Any]:
    """导出对话历史任务"""
    task_id = str(uuid.uuid4())

    logger.info(f"Starting conversation export task {task_id} for conversation {conversation_id}")

    try:
        # 加载对话历史
        history = await load_conversation_history(conversation_id)

        # 模拟导出处理
        await asyncio.sleep(2)

        export_filename = f"conversation_{conversation_id}_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}.{format}"
        export_path = f"/exports/{export_filename}"

        result = {
            "task_id": task_id,
            "conversation_id": conversation_id,
            "user_id": user_id,
            "format": format,
            "export_path": export_path,
            "message_count": len(history),
            "exported_at": datetime.utcnow().isoformat(),
        }

        logger.success(f"Conversation export task {task_id} completed: {export_path}")
        return result

    except Exception as e:
        logger.exception(f"Conversation export task {task_id} failed: {str(e)}")
        raise


@broker.task(queue_name="conversation", max_retries=2)
async def batch_conversation_processing(
    requests: list[dict[str, Any]]
) -> dict[str, Any]:
    """批量对话处理任务"""
    task_id = str(uuid.uuid4())

    logger.info(f"Starting batch conversation processing task {task_id} for {len(requests)} requests")

    try:
        results = []
        failed_requests = []

        for i, request in enumerate(requests):
            try:
                logger.info(f"Processing batch request {i+1}/{len(requests)}")

                result = await process_conversation_message(
                    conversation_id=request["conversation_id"],
                    message_id=request["message_id"],
                    content=request["content"],
                    message_type=request.get("message_type", MessageType.USER.value),
                    context=request.get("context"),
                    user_id=request.get("user_id"),
                )

                results.append(result)

            except Exception as e:
                failed_requests.append({
                    "request": request,
                    "error": str(e),
                    "error_type": type(e).__name__,
                })
                logger.warning(f"Batch request {i+1} failed: {e}")

        batch_result = {
            "task_id": task_id,
            "total_requests": len(requests),
            "successful_results": results,
            "failed_requests": failed_requests,
            "success_count": len(results),
            "failure_count": len(failed_requests),
        }

        logger.success(f"Batch conversation processing task {task_id} completed: {len(results)}/{len(requests)} successful")
        return batch_result

    except Exception as e:
        logger.exception(f"Batch conversation processing task {task_id} failed: {str(e)}")
        raise
