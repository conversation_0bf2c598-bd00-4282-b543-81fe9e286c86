"""
MongoDB 数据库配置
MongoDB Database Configuration
"""


import motor.motor_asyncio
from beanie import init_beanie
from loguru import logger
from pymongo.errors import ConnectionFailure, ServerSelectionTimeoutError

from .config import get_config

# 全局变量
_client: motor.motor_asyncio.AsyncIOMotorClient | None = None
_database: motor.motor_asyncio.AsyncIOMotorDatabase | None = None


async def get_database() -> motor.motor_asyncio.AsyncIOMotorDatabase:
    """
    获取数据库实例

    Returns:
        AsyncIOMotorDatabase: MongoDB 数据库实例
    """
    global _database
    if _database is None:
        await init_database()
    return _database


async def get_client() -> motor.motor_asyncio.AsyncIOMotorClient:
    """
    获取 MongoDB 客户端

    Returns:
        AsyncIOMotorClient: MongoDB 客户端实例
    """
    global _client
    if _client is None:
        await init_database()
    return _client


async def init_database():
    """初始化 MongoDB 数据库"""
    global _client, _database

    try:
        logger.info("Initializing MongoDB database...")

        config = get_config()

        # 创建 MongoDB 客户端
        _client = motor.motor_asyncio.AsyncIOMotorClient(
            config.database.mongodb_connection_string,
            maxPoolSize=config.database.mongodb_max_connections,
            minPoolSize=config.database.mongodb_min_connections,
            maxIdleTimeMS=config.database.mongodb_max_idle_time,
            connectTimeoutMS=config.database.mongodb_connect_timeout,
            serverSelectionTimeoutMS=config.database.mongodb_server_selection_timeout,
        )

        # 获取数据库实例
        _database = _client[config.database.mongodb_database]

        # 测试连接
        await _client.admin.command('ping')

        # 导入所有模型
        from .models.conversation import ConversationThread
        from .models.message import Message
        from .models.task import Task

        # 初始化 Beanie
        await init_beanie(
            database=_database,
            document_models=[ConversationThread, Message, Task]
        )

        logger.success(f"MongoDB database initialized successfully: {config.database.mongodb_database}")
        return True

    except Exception as e:
        logger.error(f"Failed to initialize MongoDB database: {e}")
        return False


async def close_database():
    """关闭数据库连接"""
    global _client, _database

    try:
        logger.info("Closing MongoDB connections...")

        if _client:
            _client.close()
            _client = None
            _database = None

        logger.success("MongoDB connections closed")

    except Exception as e:
        logger.error(f"Error closing MongoDB connections: {e}")


async def check_database_connection():
    """检查数据库连接"""
    try:
        config = get_config()

        # 创建临时客户端进行连接测试
        test_client = motor.motor_asyncio.AsyncIOMotorClient(
            config.database.mongodb_connection_string,
            serverSelectionTimeoutMS=5000
        )

        # 执行 ping 命令测试连接
        await test_client.admin.command('ping')
        test_client.close()

        logger.success("MongoDB connection is healthy")
        return True

    except (ConnectionFailure, ServerSelectionTimeoutError) as e:
        logger.error(f"MongoDB connection failed: {e}")
        return False
    except Exception as e:
        logger.error(f"MongoDB connection check error: {e}")
        return False


class DatabaseManager:
    """MongoDB 数据库管理器"""

    def __init__(self):
        self.client = None
        self.database = None

    async def get_client(self) -> motor.motor_asyncio.AsyncIOMotorClient:
        """获取 MongoDB 客户端"""
        return await get_client()

    async def get_database(self) -> motor.motor_asyncio.AsyncIOMotorDatabase:
        """获取数据库实例"""
        return await get_database()

    async def health_check(self) -> bool:
        """健康检查"""
        return await check_database_connection()

    async def init(self) -> bool:
        """初始化数据库"""
        return await init_database()

    async def close(self):
        """关闭数据库连接"""
        await close_database()


# 全局数据库管理器实例
db_manager = DatabaseManager()


# 依赖注入函数
async def get_database_dependency() -> motor.motor_asyncio.AsyncIOMotorDatabase:
    """
    FastAPI 依赖注入函数，用于获取数据库实例

    Returns:
        AsyncIOMotorDatabase: MongoDB 数据库实例
    """
    return await get_database()


# 数据库健康检查函数
async def database_health_check() -> dict:
    """
    数据库健康检查

    Returns:
        dict: 健康检查结果
    """
    try:
        config = get_config()
        is_healthy = await check_database_connection()

        # 获取数据库统计信息
        stats = {}
        if is_healthy:
            try:
                client = await get_client()
                db = await get_database()

                # 获取数据库统计
                db_stats = await db.command("dbStats")
                server_status = await client.admin.command("serverStatus")

                stats = {
                    "collections": db_stats.get("collections", 0),
                    "data_size": db_stats.get("dataSize", 0),
                    "storage_size": db_stats.get("storageSize", 0),
                    "indexes": db_stats.get("indexes", 0),
                    "connections": server_status.get("connections", {}).get("current", 0),
                }
            except Exception:
                # 如果获取统计信息失败，忽略错误
                pass

        return {
            "database": {
                "status": "healthy" if is_healthy else "unhealthy",
                "type": "mongodb",
                "database": config.database.mongodb_database,
                "host": config.database.mongodb_url,
                **stats
            }
        }

    except Exception as e:
        logger.error(f"Database health check failed: {e}")
        return {
            "database": {
                "status": "error",
                "type": "mongodb",
                "error": str(e),
            }
        }


# 数据库初始化脚本
async def setup_database():
    """设置数据库（用于启动脚本）"""
    try:
        logger.info("Setting up MongoDB database...")

        # 检查连接
        if not await check_database_connection():
            logger.error("Cannot connect to MongoDB")
            return False

        # 初始化数据库
        if not await init_database():
            logger.error("Failed to initialize MongoDB")
            return False

        logger.success("MongoDB setup completed")
        return True

    except Exception as e:
        logger.error(f"MongoDB setup failed: {e}")
        return False


# 创建索引的辅助函数
async def create_indexes():
    """创建数据库索引"""
    try:
        logger.info("Creating database indexes...")

        db = await get_database()

        # 对话线程索引
        await db.conversation_threads.create_index([("user_id", 1), ("created_at", -1)])
        await db.conversation_threads.create_index([("status", 1)])
        await db.conversation_threads.create_index([("last_activity_at", -1)])

        # 消息索引
        await db.messages.create_index([("conversation_id", 1), ("created_at", 1)])
        await db.messages.create_index([("message_type", 1)])
        await db.messages.create_index([("status", 1)])

        # 任务索引
        await db.tasks.create_index([("conversation_id", 1)])
        await db.tasks.create_index([("status", 1), ("created_at", -1)])
        await db.tasks.create_index([("task_type", 1)])
        await db.tasks.create_index([("user_id", 1)])

        logger.success("Database indexes created successfully")

    except Exception as e:
        logger.error(f"Failed to create indexes: {e}")


# 导出主要组件
__all__ = [
    "get_database",
    "get_client",
    "get_database_dependency",
    "init_database",
    "close_database",
    "check_database_connection",
    "database_health_check",
    "setup_database",
    "create_indexes",
    "DatabaseManager",
    "db_manager",
]
