# Motion Agent API 路由统一管理文档
# Motion Agent API Routes Unified Management Documentation

## 概述 Overview

本文档描述了Motion Agent后端API的统一路由管理结构。所有API端点已按功能模块组织到不同的路由器中，实现了清晰的分离和统一管理。

This document describes the unified router management structure for the Motion Agent backend API. All API endpoints have been organized into different routers by functional modules, achieving clear separation and unified management.

## 路由器结构 Router Structure

### 1. 健康检查路由器 Health Router (`/health`)
**文件位置**: `backend/routers/health.py`
**前缀**: `/health`
**标签**: `["Health"]`

#### 端点 Endpoints:
- `GET /health/` - 根路径健康检查
- `GET /health/check` - 详细健康检查
- `GET /health/services` - 服务状态检查

### 2. 动作生成路由器 Motion Router (`/motion`)
**文件位置**: `backend/routers/motion.py`
**前缀**: `/motion`
**标签**: `["Motion"]`

#### 端点 Endpoints:
- `POST /motion/generate` - 基础动作生成
- `POST /motion/generate-advanced` - 高级动作生成（使用LangGraph）

### 3. 动画生成路由器 Animation Router (`/animation`)
**文件位置**: `backend/routers/animation.py`
**前缀**: `/animation`
**标签**: `["Animation"]`

#### 端点 Endpoints:
- `GET /animation/presets` - 获取动画预设
- `GET /animation/examples` - 获取示例请求
- `POST /animation/generate` - 专业动画生成
- `GET /animation/download/{file_path:path}` - 下载动画文件
- `GET /animation/health` - 动画系统健康检查

### 4. 对话管理路由器 Conversations Router (`/conversations`)
**文件位置**: `backend/routers/conversations.py`
**前缀**: `/conversations`
**标签**: `["Conversations"]`

#### 端点 Endpoints:
- `POST /conversations/` - 创建新对话线程
- `GET /conversations/` - 获取对话列表
- `GET /conversations/{conversation_id}` - 获取对话详情
- `PUT /conversations/{conversation_id}` - 更新对话
- `DELETE /conversations/{conversation_id}` - 删除对话
- `GET /conversations/{conversation_id}/history` - 获取对话历史
- `POST /conversations/{conversation_id}/messages` - 发送消息到对话
- `POST /conversations/{conversation_id}/animation` - 开始动画生成

### 5. 任务管理路由器 Tasks Router (`/tasks`)
**文件位置**: `backend/routers/tasks.py`
**前缀**: `/tasks`
**标签**: `["Tasks"]`

#### 端点 Endpoints:
- `POST /tasks/` - 创建新任务
- `GET /tasks/` - 获取任务列表
- `GET /tasks/{task_id}` - 获取任务详情
- `PUT /tasks/{task_id}` - 更新任务
- `POST /tasks/{task_id}/cancel` - 取消任务
- `GET /tasks/stats/overview` - 获取任务统计
- `POST /tasks/animation` - 提交动画生成任务
- `POST /tasks/conversation` - 提交对话处理任务
- `GET /tasks/conversation/{conversation_id}` - 获取对话相关任务

## 统一管理 Unified Management

### 路由器注册 Router Registration
所有路由器在 `backend/routers/__init__.py` 中统一导出：

```python
from .animation import router as animation_router
from .conversations import router as conversations_router
from .health import router as health_router
from .motion import router as motion_router
from .tasks import router as tasks_router

# 路由器列表，用于批量注册
ROUTERS = [
    animation_router,
    conversations_router,
    health_router,
    motion_router,
    tasks_router,
]
```

### 主应用集成 Main App Integration
在 `backend/app.py` 中批量注册所有路由器：

```python
from .routers import ROUTERS

# Include all routers
for router in ROUTERS:
    app.include_router(router)
```

## 数据模型 Data Models

### 基础模型 Basic Models
- `TextInput` - 文本输入模型
- `AdvancedTextInput` - 高级文本输入模型
- `MotionRequest` - 动作请求模型
- `MotionResponse` - 动作响应模型
- `AnimationRequest` - 动画请求模型
- `AnimationResponse` - 动画响应模型

### 对话模型 Conversation Models
- `ConversationThreadCreate` - 创建对话线程
- `ConversationThreadUpdate` - 更新对话线程
- `ConversationThreadResponse` - 对话线程响应
- `ConversationThreadList` - 对话线程列表
- `MessageCreate` - 创建消息
- `MessageResponse` - 消息响应

### 任务模型 Task Models
- `TaskCreate` - 创建任务
- `TaskUpdate` - 更新任务
- `TaskResponse` - 任务响应
- `TaskList` - 任务列表
- `TaskFilter` - 任务过滤器
- `TaskStats` - 任务统计

## 优势 Benefits

1. **模块化设计** - 每个功能模块独立管理
2. **清晰的职责分离** - 不同类型的端点分布在不同的路由器中
3. **统一的错误处理** - 所有路由器使用一致的错误处理模式
4. **易于维护** - 新功能可以轻松添加到相应的路由器中
5. **一致的日志记录** - 使用loguru进行统一的日志管理
6. **类型安全** - 使用Pydantic模型确保数据验证

## 配置 Configuration

所有路由器都使用统一的配置管理：
- 数据库连接配置
- 日志配置
- CORS配置
- API版本管理

## 下一步 Next Steps

1. 实现各个服务的实际健康检查
2. 添加认证和授权中间件
3. 实现API版本控制
4. 添加请求限流和缓存
5. 完善错误处理和监控
