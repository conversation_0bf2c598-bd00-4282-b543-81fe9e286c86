import os
from typing import Any

import bpy


class FBXExporter:
    def __init__(self):
        self.export_settings = {
            "use_selection": True,
            "use_mesh_modifiers": True,
            "use_mesh_edges": False,
            "use_tspace": False,
            "use_custom_props": False,
            "add_leaf_bones": False,
            "bake_anim": True,
            "bake_anim_use_all_bones": True,
            "bake_anim_step": 1,
            "bake_anim_simplify_factor": 1.0,
            "path_mode": "COPY",
            "embed_textures": False,
            "batch_mode": "OFF",
            "use_metadata": True,
        }

    def export_fbx(self, filepath: str, animation_data: dict[str, Any]) -> bool:
        try:
            # 设置导出选项
            for key, value in self.export_settings.items():
                setattr(bpy.context.scene, f"fbx_export_{key}", value)

            # 导出FBX
            bpy.ops.export_scene.fbx(filepath=filepath, **self.export_settings)
            return True
        except Exception as e:
            print(f"Export failed: {str(e)}")
            return False

    def validate_fbx(self, filepath: str) -> bool:
        # 实现FBX文件验证逻辑
        if not os.path.exists(filepath):
            return False

        # 检查文件大小
        if os.path.getsize(filepath) == 0:
            return False

        return True
