from typing import Any


class ExpressionAnimator:
    def __init__(self):
        self.expression_types = {
            "happy": ["smile", "eyes_wide"],
            "angry": ["frown", "eyes_narrow"],
            "sad": ["frown", "eyes_down"],
            "surprised": ["mouth_open", "eyes_wide"],
        }

    def create_expression(
        self, expression_type: str, intensity: float
    ) -> dict[str, Any]:
        if expression_type not in self.expression_types:
            return {}

        expression_data = {}
        for morph in self.expression_types[expression_type]:
            expression_data[morph] = intensity

        return expression_data

    def blend_expressions(
        self, expr1: dict[str, Any], expr2: dict[str, Any], blend_factor: float
    ) -> dict[str, Any]:
        blended = {}
        for key in set(expr1.keys()) | set(expr2.keys()):
            val1 = expr1.get(key, 0)
            val2 = expr2.get(key, 0)
            blended[key] = (1 - blend_factor) * val1 + blend_factor * val2
        return blended
