import re
from typing import Any


class ActionExtractor:
    def __init__(self):
        self.patterns = {
            "duration": r"(\d+)秒",
            "direction": r"向([东西南北上下左右前后])",
            "steps": r"(\d+)步",
            "angle": r"(\d+)度",
            "hand": r"(左手|右手|双手)",
        }

    def extract(self, text: str) -> dict[str, Any]:
        params = {}
        for key, pattern in self.patterns.items():
            match = re.search(pattern, text)
            if match:
                params[key] = match.group(1)
        return params
