"""
Motion Agent API 路由器
Motion Agent API Routers

统一管理所有API路由器
Unified management of all API routers
"""

from .animation import router as animation_router
from .conversations import router as conversations_router
from .health import router as health_router
from .motion import router as motion_router
from .tasks import router as tasks_router

# 导出所有路由器
__all__ = [
    "animation_router",
    "conversations_router",
    "health_router",
    "motion_router",
    "tasks_router",
]

# 路由器列表，用于批量注册
ROUTERS = [
    animation_router,
    conversations_router,
    health_router,
    motion_router,
    tasks_router,
]
