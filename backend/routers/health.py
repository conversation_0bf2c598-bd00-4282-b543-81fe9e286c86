"""
健康检查 API 路由
Health Check API Routes
"""

from fastapi import APIRouter
from loguru import logger

from ..database import database_health_check

# 创建路由器
router = APIRouter(prefix="/health", tags=["Health"])


@router.get("/")
async def root():
    """根路径健康检查"""
    logger.info("Root health endpoint accessed")
    return {
        "message": "Professional Motion Agent API is running",
        "version": "3.0.0",
        "features": [
            "Professional Game Animator",
            "Natural Language to Animation",
            "Unified Animator Functions",
            "Blender Integration",
            "FBX Export",
            "Conversation Threads",
            "Task Queue Management",
            "MongoDB Database",
        ],
    }


@router.get("/check")
async def health_check():
    """详细健康检查"""
    logger.debug("Health check endpoint accessed")

    # 获取数据库健康状态
    db_health = await database_health_check()

    return {
        "status": "healthy",
        "version": "3.0.0",
        "services": {
            "nlu_pipeline": "ready",  # TODO: 从主应用获取实际状态
            "langgraph_pipeline": "ready",  # TODO: 从主应用获取实际状态
            "professional_animator": "ready",
            "database": db_health.get("database", {}).get("status", "unknown"),
            "taskiq": "ready",  # TODO: 实现 taskiq 健康检查
        },
        "features": {
            "langchain": True,
            "langgraph": True,
            "loguru_logging": True,
            "ruff_linting": True,
            "professional_animator": True,
            "unified_animator": True,
            "blender_integration": True,
            "fbx_export": True,
            "conversation_threads": True,
            "task_queue": True,
            "async_processing": True,
            "mongodb_database": True,
        },
        "database": db_health.get("database", {}),
    }


@router.get("/services")
async def service_status():
    """服务状态检查"""
    logger.debug("Service status endpoint accessed")

    # TODO: 实现各个服务的实际状态检查
    services = {
        "api_server": {"status": "running", "uptime": "unknown"},
        "database": {"status": "connected", "response_time": "unknown"},
        "task_queue": {"status": "running", "workers": "unknown"},
        "nlu_pipeline": {"status": "ready", "models_loaded": True},
        "animation_pipeline": {"status": "ready", "blender_available": True},
    }

    return {
        "timestamp": "2024-01-01T00:00:00Z",  # TODO: 使用实际时间戳
        "services": services,
        "overall_status": "healthy",
    }
