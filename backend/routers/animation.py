"""
动画生成 API 路由
Animation Generation API Routes
"""

import os
from typing import Any

from fastapi import APIRouter, HTTPException
from fastapi.responses import FileResponse
from loguru import logger
from pydantic import BaseModel

# 导入必要的模型
try:
    from ..models.task import TaskStatus
except ImportError:
    # 如果导入失败，创建一个简单的枚举
    from enum import Enum
    class TaskStatus(Enum):
        PENDING = "pending"
        RUNNING = "running"
        COMPLETED = "completed"
        FAILED = "failed"

# 创建路由器
router = APIRouter(prefix="/animation", tags=["Animation"])


# 数据模型
class AnimationRequest(BaseModel):
    text: str
    character_id: str = "default"
    quality_target: str = "game_ready"
    frame_rate: int = 30
    export_format: str = "fbx"
    context: dict[str, Any] | None = None
    reference_animations: list = []


class AnimationResponse(BaseModel):
    success: bool
    animation_sequence: dict[str, Any] | None = None
    fbx_file_path: str | None = None
    original_text: str
    processed_actions: list = []
    quality_report: dict[str, Any] = {}
    error_message: str | None = None
    warnings: list = []
    processing_time: float | None = None


@router.get("/presets")
async def get_animation_presets():
    """获取可用的动画预设"""
    logger.debug("Animation presets endpoint accessed")

    try:
        from ..animation.animation_presets import AnimationPresetsLibrary
        presets_library = AnimationPresetsLibrary()
        return presets_library.export_presets_for_api()
    except ImportError:
        logger.warning("Animation presets library not available, using fallback")
        return {
            "locomotion": ["walk", "run", "sprint", "jump", "hop", "skip"],
            "acrobatic": ["backflip", "frontflip", "sideflip", "roll", "cartwheel"],
            "combat": ["punch", "kick", "slash", "block", "dodge", "parry"],
            "gestures": ["wave", "point", "clap", "thumbs_up", "salute"],
            "expressions": ["smile", "frown", "surprise", "anger", "sad", "happy"],
            "idle": ["breathing", "looking_around", "fidget", "stretch"],
        }


@router.get("/examples")
async def get_example_requests():
    """获取示例请求"""
    logger.debug("Animation examples endpoint accessed")
    return {
        "simple_examples": [
            {"text": "向前走三步", "description": "简单移动动画", "complexity": "basic"},
            {"text": "跳跃然后落地", "description": "基础跳跃动作", "complexity": "basic"},
            {"text": "挥手打招呼", "description": "手势动画", "complexity": "basic"},
        ],
        "intermediate_examples": [
            {"text": "冲刺攻击然后防御", "description": "战斗动作组合", "complexity": "intermediate"},
            {"text": "后空翻360度", "description": "特技动作", "complexity": "intermediate"},
            {"text": "愤怒表情然后攻击", "description": "表情+动作组合", "complexity": "intermediate"},
        ],
        "advanced_examples": [
            {
                "text": "生成一个后空翻720度之后，向前五步走，然后转身，并向左迈一步",
                "description": "复杂动作序列",
                "complexity": "advanced",
            },
            {"text": "角色快速冲刺，然后跳跃攻击，最后防御姿态", "description": "战斗连招", "complexity": "advanced"},
            {
                "text": "慢慢走向前方，挥手打招呼，然后坐下休息",
                "description": "日常动作序列",
                "complexity": "advanced",
            },
        ],
    }


@router.post("/generate", response_model=AnimationResponse)
async def generate_professional_animation(request: AnimationRequest):
    """使用专业管道生成动画"""
    logger.info(f"Received professional animation request: {request.text}")

    # 创建任务服务实例
    from ..services.task_service import TaskService
    task_service = TaskService()

    # 创建任务记录
    task = None
    try:
        task = await task_service.create_animation_task(
            text=request.text,
            character_id=request.character_id,
            user_id=None,  # TODO: 从认证中获取用户ID
            context={
                "quality_target": request.quality_target,
                "frame_rate": request.frame_rate,
                "export_format": request.export_format,
            }
        )
        logger.info(f"Created animation task: {task.task_id}")

        # 更新任务状态为运行中
        await task_service.update_task_status(task.task_id, TaskStatus.RUNNING)

    except Exception as e:
        logger.error(f"Failed to create animation task: {e}")

    try:
        # 导入专业动画管道
        import sys
        sys.path.append(os.path.dirname(__file__))

        # 尝试导入专业动画模块
        try:
            from ..animation.models import AnimationRequest as ProfAnimationRequest
            from ..animation.professional_pipeline import ProfessionalAnimationPipeline

            # 创建管道
            pipeline = ProfessionalAnimationPipeline()

            # 转换请求到专业格式
            prof_request = ProfAnimationRequest(
                text=request.text,
                character_id=request.character_id,
                quality_target=request.quality_target,
                frame_rate=request.frame_rate,
                export_format=request.export_format,
            )

            # 处理动画请求
            prof_response = await pipeline.process_animation_request(prof_request)

            # 转换专业响应到API响应格式
            api_response = AnimationResponse(
                success=prof_response.success,
                animation_sequence={
                    "id": prof_response.animation_sequence.id if prof_response.animation_sequence else None,
                    "name": prof_response.animation_sequence.name if prof_response.animation_sequence else None,
                    "actions": [action.name for action in prof_response.animation_sequence.actions]
                    if prof_response.animation_sequence
                    else [],
                    "total_duration": prof_response.animation_sequence.total_duration
                    if prof_response.animation_sequence
                    else 0,
                    "frame_rate": prof_response.animation_sequence.frame_rate if prof_response.animation_sequence else 30,
                    "total_frames": prof_response.animation_sequence.total_frames
                    if prof_response.animation_sequence
                    else 0,
                    "character_id": prof_response.animation_sequence.character_id
                    if prof_response.animation_sequence
                    else request.character_id,
                }
                if prof_response.animation_sequence
                else None,
                fbx_file_path=prof_response.fbx_file_path,
                original_text=prof_response.original_text,
                processed_actions=prof_response.processed_actions,
                quality_report=prof_response.quality_report,
                error_message=prof_response.error_message,
                warnings=prof_response.warnings,
                processing_time=prof_response.processing_time,
            )

            # 更新任务状态为完成
            if task:
                try:
                    status = TaskStatus.COMPLETED if prof_response.success else TaskStatus.FAILED
                    await task_service.update_task_status(
                        task.task_id,
                        status,
                        output_data={
                            "fbx_file_path": prof_response.fbx_file_path,
                            "processing_time": prof_response.processing_time,
                            "success": prof_response.success,
                        },
                        error_message=prof_response.error_message if not prof_response.success else None
                    )
                    logger.info(f"Updated task {task.task_id} status to {status.value}")
                except Exception as e:
                    logger.error(f"Failed to update task status: {e}")

            logger.success("Professional animation generation completed")
            return api_response

        except ImportError as import_error:
            logger.warning(f"Professional animation module not available: {import_error}")
            # 回退到基础动画生成
            return AnimationResponse(
                success=True,
                original_text=request.text,
                animation_sequence={
                    "actions": ["walk", "wave"],
                    "duration": 5.0,
                    "character_id": request.character_id,
                },
                processed_actions=["walk", "wave"],
                quality_report={"status": "basic_fallback"},
                warnings=["Using basic animation fallback - professional module not available"],
            )

    except Exception as e:
        logger.exception(f"Error in professional animation generation: {e}")

        # 更新任务状态为失败
        if task:
            try:
                await task_service.update_task_status(
                    task.task_id,
                    TaskStatus.FAILED,
                    error_message=str(e)
                )
                logger.info(f"Updated task {task.task_id} status to FAILED")
            except Exception as update_error:
                logger.error(f"Failed to update task status: {update_error}")

        return AnimationResponse(success=False, original_text=request.text, error_message=str(e))


@router.get("/download/{file_path:path}")
async def download_animation_file(file_path: str):
    """下载生成的动画文件"""
    logger.info(f"Download request for file: {file_path}")

    # 安全检查：确保文件在输出目录中
    if not file_path.startswith("output/animations/"):
        file_path = f"output/animations/{file_path}"

    # 检查文件是否存在
    if not os.path.exists(file_path):
        logger.warning(f"File not found: {file_path}")
        raise HTTPException(status_code=404, detail="File not found")

    # 获取文件信息
    file_size = os.path.getsize(file_path)
    filename = os.path.basename(file_path)

    logger.info(f"Serving file: {filename} ({file_size} bytes)")

    return FileResponse(path=file_path, filename=filename, media_type="application/octet-stream")


@router.get("/health")
async def animation_health_check():
    """动画系统健康检查"""
    logger.debug("Animation health check endpoint accessed")

    try:
        # 检查输出目录
        output_dir = "output/animations"
        output_dir_exists = os.path.exists(output_dir)
        output_dir_writable = os.access(output_dir, os.W_OK) if output_dir_exists else False

        # 尝试检查专业动画模块
        try:
            from ..animation.professional_pipeline import ProfessionalAnimationPipeline
            professional_available = True
            blender_available = True  # TODO: 实际检查Blender可用性
        except ImportError:
            professional_available = False
            blender_available = False

        return {
            "status": "healthy" if output_dir_exists and output_dir_writable else "degraded",
            "professional_animation_available": professional_available,
            "blender_available": blender_available,
            "output_directory": output_dir,
            "output_directory_exists": output_dir_exists,
            "output_writable": output_dir_writable,
            "supported_formats": ["fbx", "blend"],
            "animation_presets_loaded": True,
        }

    except Exception as e:
        logger.exception(f"Error in animation health check: {e}")
        return {
            "status": "error",
            "error_message": str(e),
            "professional_animation_available": False,
            "blender_available": False,
        }


@router.post("/validate-fbx")
async def validate_fbx_file(file_path: str):
    """验证FBX文件质量"""
    logger.info(f"FBX validation request for: {file_path}")

    try:
        from ..animation.fbx_validator import FBXValidator
        validator = FBXValidator()

        # 确保文件路径安全
        if not file_path.startswith("output/animations/"):
            file_path = f"output/animations/{file_path}"

        validation_result = validator.validate_fbx_file(file_path)

        return {
            "success": True,
            "validation_result": validation_result,
            "report": validator.generate_validation_report(validation_result)
        }

    except Exception as e:
        logger.error(f"FBX validation error: {e}")
        return {
            "success": False,
            "error_message": str(e)
        }


@router.get("/presets/search")
async def search_animation_presets(keywords: str):
    """搜索动画预设"""
    logger.debug(f"Searching presets with keywords: {keywords}")

    try:
        from ..animation.animation_presets import AnimationPresetsLibrary
        presets_library = AnimationPresetsLibrary()

        keyword_list = [kw.strip() for kw in keywords.split(",")]
        results = presets_library.search_presets(keyword_list)

        return {
            "success": True,
            "results": [preset.__dict__ for preset in results],
            "count": len(results)
        }

    except Exception as e:
        logger.error(f"Preset search error: {e}")
        return {
            "success": False,
            "error_message": str(e)
        }


@router.get("/transition-recommendations")
async def get_transition_recommendations(from_action: str, to_action: str):
    """获取动作过渡建议"""
    logger.debug(f"Getting transition recommendations: {from_action} -> {to_action}")

    try:
        from ..animation.transition_optimizer import TransitionOptimizer
        optimizer = TransitionOptimizer()

        recommendations = optimizer.get_transition_recommendations(from_action, to_action)

        return {
            "success": True,
            "recommendations": recommendations
        }

    except Exception as e:
        logger.error(f"Transition recommendations error: {e}")
        return {
            "success": False,
            "error_message": str(e)
        }


@router.post("/quality-check")
async def check_animation_quality(animation_data: dict):
    """检查动画质量"""
    logger.info("Animation quality check requested")

    try:
        from ..animation.quality_checker import AnimationQualityChecker
        checker = AnimationQualityChecker()

        quality_result = checker.check_animation_quality(animation_data)

        return {
            "success": True,
            "quality_analysis": quality_result
        }

    except Exception as e:
        logger.error(f"Quality check error: {e}")
        return {
            "success": False,
            "error_message": str(e)
        }
