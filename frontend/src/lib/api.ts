import axios from 'axios';
import {
  AnimationRequest,
  AnimationResponse,
  AnimationPresets,
  ExampleRequest,
  HealthStatus,
  MotionRequest,
  AdvancedMotionRequest,
  MotionResponse,
  ConversationThread,
  ConversationCreate,
  ConversationUpdate,
  ConversationList,
  ConversationHistory,
  MessageCreate,
  Task,
  TaskCreate,
  TaskUpdate,
  TaskList,
  TaskStats
} from '@/types/motion';

// API Base URL - Backend runs on port 9000
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:9000';

// Create axios instance with default config
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000, // 30 seconds timeout for animation generation
  headers: {
    'Content-Type': 'application/json',
  },
});

// API Functions

/**
 * Health check endpoint
 */
export const checkHealth = async (): Promise<HealthStatus> => {
  const response = await api.get('/health');
  return response.data;
};

/**
 * Professional animation generation
 */
export const generateProfessionalAnimation = async (request: AnimationRequest): Promise<AnimationResponse> => {
  const response = await api.post('/animation/generate', request);
  return response.data;
};

/**
 * Get animation presets
 */
export const getAnimationPresets = async (): Promise<AnimationPresets> => {
  const response = await api.get('/animation/presets');
  return response.data;
};

/**
 * Get example requests
 */
export const getExampleRequests = async (): Promise<{
  simple_examples: ExampleRequest[];
  intermediate_examples: ExampleRequest[];
  advanced_examples: ExampleRequest[];
}> => {
  const response = await api.get('/animation/examples');
  return response.data;
};

/**
 * Animation system health check
 */
export const checkAnimationHealth = async () => {
  const response = await api.get('/animation/health');
  return response.data;
};

/**
 * Motion Generation APIs
 */

/**
 * Basic motion generation
 */
export const generateMotion = async (request: MotionRequest): Promise<MotionResponse> => {
  const response = await api.post('/motion/generate', {
    text: request.text,
    character_id: request.character_id || 'default'
  });
  return response.data;
};

/**
 * Advanced motion generation with LangGraph
 */
export const generateAdvancedMotion = async (request: AdvancedMotionRequest): Promise<MotionResponse> => {
  const response = await api.post('/motion/generate-advanced', {
    text: request.text,
    character_id: request.character_id || 'default',
    use_langgraph: request.use_langgraph !== false,
    context: request.context
  });
  return response.data;
};

/**
 * Conversation Management APIs
 */

/**
 * Create a new conversation thread
 */
export const createConversation = async (data: ConversationCreate): Promise<ConversationThread> => {
  const response = await api.post('/conversations/', data);
  return response.data;
};

/**
 * Get conversation list with filters
 */
export const getConversations = async (params?: {
  status?: string;
  user_id?: string;
  character_id?: string;
  is_pinned?: boolean;
  search?: string;
  page?: number;
  page_size?: number;
  sort_by?: string;
  sort_order?: string;
}): Promise<ConversationList> => {
  const response = await api.get('/conversations/', { params });
  return response.data;
};

/**
 * Get conversation details
 */
export const getConversation = async (conversationId: string): Promise<ConversationThread> => {
  const response = await api.get(`/conversations/${conversationId}`);
  return response.data;
};

/**
 * Update conversation
 */
export const updateConversation = async (
  conversationId: string,
  data: ConversationUpdate
): Promise<ConversationThread> => {
  const response = await api.put(`/conversations/${conversationId}`, data);
  return response.data;
};

/**
 * Delete conversation
 */
export const deleteConversation = async (conversationId: string): Promise<void> => {
  await api.delete(`/conversations/${conversationId}`);
};

/**
 * Get conversation history
 */
export const getConversationHistory = async (
  conversationId: string,
  params?: { page?: number; page_size?: number }
): Promise<ConversationHistory> => {
  const response = await api.get(`/conversations/${conversationId}/history`, { params });
  return response.data;
};

/**
 * Send message to conversation
 */
export const sendMessage = async (
  conversationId: string,
  message: MessageCreate
): Promise<any> => {
  const response = await api.post(`/conversations/${conversationId}/messages`, message);
  return response.data;
};

/**
 * Start animation generation from conversation
 */
export const startConversationAnimation = async (
  conversationId: string,
  data: {
    text: string;
    character_id?: string;
    context?: Record<string, any>;
    message_id?: string;
  }
): Promise<any> => {
  const response = await api.post(`/conversations/${conversationId}/animation`, data);
  return response.data;
};

/**
 * Task Management APIs
 */

/**
 * Create a new task
 */
export const createTask = async (data: TaskCreate): Promise<Task> => {
  const response = await api.post('/tasks/', data);
  return response.data;
};

/**
 * Get task list with filters
 */
export const getTasks = async (params?: {
  task_type?: string;
  status?: string;
  priority?: string;
  conversation_id?: string;
  user_id?: string;
  is_system_task?: boolean;
  page?: number;
  page_size?: number;
  sort_by?: string;
  sort_order?: string;
}): Promise<TaskList> => {
  const response = await api.get('/tasks/', { params });
  return response.data;
};

/**
 * Get task details
 */
export const getTask = async (taskId: string): Promise<Task> => {
  const response = await api.get(`/tasks/${taskId}`);
  return response.data;
};

/**
 * Update task
 */
export const updateTask = async (taskId: string, data: TaskUpdate): Promise<Task> => {
  const response = await api.put(`/tasks/${taskId}`, data);
  return response.data;
};

/**
 * Cancel task
 */
export const cancelTask = async (taskId: string): Promise<any> => {
  const response = await api.post(`/tasks/${taskId}/cancel`);
  return response.data;
};

/**
 * Get task statistics
 */
export const getTaskStats = async (): Promise<TaskStats> => {
  const response = await api.get('/tasks/stats/overview');
  return response.data;
};

/**
 * Submit animation generation task
 */
export const submitAnimationTask = async (data: {
  text: string;
  character_id?: string;
  context?: Record<string, any>;
  user_id?: string;
}): Promise<Task> => {
  const response = await api.post('/tasks/animation', data);
  return response.data;
};

/**
 * Submit conversation processing task
 */
export const submitConversationTask = async (data: {
  conversation_id: string;
  action: string;
  parameters?: Record<string, any>;
}): Promise<Task> => {
  const response = await api.post('/tasks/conversation', data);
  return response.data;
};

/**
 * Get tasks for a conversation
 */
export const getConversationTasks = async (conversationId: string): Promise<Task[]> => {
  const response = await api.get(`/tasks/conversation/${conversationId}`);
  return response.data;
};

// Error handling wrapper
export const handleApiError = (error: unknown): string => {
  if (error && typeof error === 'object' && 'response' in error) {
    const axiosError = error as { response: { data?: { detail?: string; message?: string } } };
    // Server responded with error status
    return axiosError.response.data?.detail || axiosError.response.data?.message || 'Server error occurred';
  } else if (error && typeof error === 'object' && 'request' in error) {
    // Request was made but no response received
    return 'Unable to connect to the server. Please check if the backend is running.';
  } else {
    // Something else happened
    const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';
    return errorMessage;
  }
};
