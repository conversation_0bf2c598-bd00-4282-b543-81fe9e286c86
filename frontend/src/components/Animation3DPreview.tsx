'use client';

import { useEffect, useRef, useState } from 'react';
import { 
  Play, 
  Pause, 
  RotateCcw, 
  Volume2, 
  VolumeX,
  Maximize,
  X,
  AlertCircle
} from 'lucide-react';

interface Animation3DPreviewProps {
  fbxUrl?: string;
  animationName?: string;
  isOpen: boolean;
  onClose: () => void;
}

export default function Animation3DPreview({ 
  fbxUrl, 
  animationName, 
  isOpen, 
  onClose 
}: Animation3DPreviewProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isMuted, setIsMuted] = useState(true);
  const [progress, setProgress] = useState(0);
  const [duration, setDuration] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!isOpen || !fbxUrl) return;

    // Initialize 3D preview
    initializePreview();

    return () => {
      // Cleanup
      cleanup();
    };
  }, [isOpen, fbxUrl]);

  const initializePreview = async () => {
    try {
      setLoading(true);
      setError(null);

      // For now, we'll show a placeholder since FBX loading requires Three.js
      // In a real implementation, you would:
      // 1. Load Three.js and FBX loader
      // 2. Create a scene, camera, renderer
      // 3. Load the FBX file
      // 4. Set up animation controls

      // Simulate loading
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // For demo purposes, we'll show a placeholder
      setError('3D preview not yet implemented. Please download the FBX file to view in 3D software.');
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load animation');
    } finally {
      setLoading(false);
    }
  };

  const cleanup = () => {
    // Cleanup Three.js resources
  };

  const handlePlayPause = () => {
    setIsPlaying(!isPlaying);
    // Control animation playback
  };

  const handleRestart = () => {
    setProgress(0);
    setIsPlaying(true);
    // Restart animation
  };

  const handleProgressChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newProgress = parseFloat(e.target.value);
    setProgress(newProgress);
    // Seek to position
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-2xl w-full max-w-4xl h-full max-h-[90vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b">
          <div>
            <h3 className="text-lg font-semibold">3D Animation Preview</h3>
            {animationName && (
              <p className="text-sm text-gray-600">{animationName}</p>
            )}
          </div>
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* 3D Viewport */}
        <div className="flex-1 relative bg-gray-900">
          {loading ? (
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="text-center text-white">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
                <p>Loading 3D animation...</p>
              </div>
            </div>
          ) : error ? (
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="text-center text-white max-w-md">
                <AlertCircle className="w-12 h-12 text-yellow-400 mx-auto mb-4" />
                <h4 className="text-lg font-medium mb-2">Preview Not Available</h4>
                <p className="text-gray-300 text-sm">{error}</p>
                <div className="mt-4 p-4 bg-gray-800 rounded-lg text-left">
                  <h5 className="font-medium mb-2">Recommended 3D Software:</h5>
                  <ul className="text-sm space-y-1">
                    <li>• Autodesk Maya</li>
                    <li>• 3ds Max</li>
                    <li>• Blender (Free)</li>
                    <li>• Cinema 4D</li>
                    <li>• Unity 3D</li>
                  </ul>
                </div>
              </div>
            </div>
          ) : (
            <>
              <canvas
                ref={canvasRef}
                className="w-full h-full"
                style={{ display: 'block' }}
              />
              
              {/* 3D Controls Overlay */}
              <div className="absolute top-4 right-4 bg-black bg-opacity-50 rounded-lg p-2">
                <div className="text-white text-xs space-y-1">
                  <div>Mouse: Rotate view</div>
                  <div>Wheel: Zoom</div>
                  <div>Right-click: Pan</div>
                </div>
              </div>
            </>
          )}
        </div>

        {/* Animation Controls */}
        <div className="p-4 border-t bg-gray-50">
          <div className="flex items-center space-x-4">
            {/* Play/Pause */}
            <button
              onClick={handlePlayPause}
              disabled={loading || !!error}
              className="p-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isPlaying ? <Pause className="w-5 h-5" /> : <Play className="w-5 h-5" />}
            </button>

            {/* Restart */}
            <button
              onClick={handleRestart}
              disabled={loading || !!error}
              className="p-2 text-gray-600 hover:bg-gray-200 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <RotateCcw className="w-5 h-5" />
            </button>

            {/* Progress Bar */}
            <div className="flex-1">
              <input
                type="range"
                min="0"
                max="100"
                value={progress}
                onChange={handleProgressChange}
                disabled={loading || !!error}
                className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer disabled:opacity-50"
              />
              <div className="flex justify-between text-xs text-gray-500 mt-1">
                <span>{Math.floor(progress * duration / 100)}s</span>
                <span>{duration}s</span>
              </div>
            </div>

            {/* Volume */}
            <button
              onClick={() => setIsMuted(!isMuted)}
              disabled={loading || !!error}
              className="p-2 text-gray-600 hover:bg-gray-200 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isMuted ? <VolumeX className="w-5 h-5" /> : <Volume2 className="w-5 h-5" />}
            </button>

            {/* Fullscreen */}
            <button
              onClick={() => {
                // Toggle fullscreen
                if (document.fullscreenElement) {
                  document.exitFullscreen();
                } else {
                  document.documentElement.requestFullscreen();
                }
              }}
              disabled={loading || !!error}
              className="p-2 text-gray-600 hover:bg-gray-200 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <Maximize className="w-5 h-5" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
