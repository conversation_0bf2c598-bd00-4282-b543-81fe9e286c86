'use client';

import { useState } from 'react';
import {
  Activity,
  MessageCircle,
  Play,
  BarChart3,
  Monitor,
  Menu,
  X,
  Home,
  Zap,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import { HealthStatus } from '@/types/motion';

interface NavigationProps {
  currentPage: string;
  onPageChange: (page: string) => void;
  backendStatus?: 'checking' | 'online' | 'offline';
  healthData?: HealthStatus | null;
}

export default function Navigation({
  currentPage,
  onPageChange,
  backendStatus = 'checking',
  healthData
}: NavigationProps) {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const navigationItems = [
    {
      id: 'chat',
      name: 'Chat',
      icon: MessageCircle,
      description: 'ChatGPT-style Interface'
    },
    {
      id: 'home',
      name: 'Home',
      icon: Home,
      description: 'Animation Generation'
    },
    {
      id: 'motion',
      name: 'Motion',
      icon: Play,
      description: 'Motion Generation'
    },
    {
      id: 'conversations',
      name: 'Conversations',
      icon: MessageCircle,
      description: 'Conversation Management'
    },
    {
      id: 'tasks',
      name: 'Tasks',
      icon: Activity,
      description: 'Task Monitoring'
    },
    {
      id: 'status',
      name: 'Status',
      icon: Monitor,
      description: 'System Dashboard'
    }
  ];

  const handlePageChange = (pageId: string) => {
    onPageChange(pageId);
    setIsMobileMenuOpen(false);
  };

  const StatusIndicator = () => (
    <div className="flex items-center gap-2 text-sm">
      {backendStatus === 'checking' && (
        <>
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
          <span className="text-gray-600">Checking backend...</span>
        </>
      )}
      {backendStatus === 'online' && (
        <>
          <CheckCircle className="w-4 h-4 text-green-500" />
          <span className="text-green-600">Backend Online</span>
          {healthData && (
            <span className="text-gray-500">v{healthData.version}</span>
          )}
        </>
      )}
      {backendStatus === 'offline' && (
        <>
          <AlertCircle className="w-4 h-4 text-red-500" />
          <span className="text-red-600">Backend Offline</span>
        </>
      )}
    </div>
  );

  return (
    <>
      {/* Desktop Navigation */}
      <nav className="hidden md:flex bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 w-full">
          <div className="flex justify-between h-16">
            {/* Logo */}
            <div className="flex items-center">
              <div className="flex items-center gap-3">
                <Zap className="w-8 h-8 text-blue-600" />
                <div>
                  <h1 className="text-xl font-bold text-gray-900">Motion Agent</h1>
                  <p className="text-xs text-gray-600">Professional 3D Animation</p>
                </div>
              </div>
            </div>

            {/* Navigation Items and Status */}
            <div className="flex items-center gap-6">
              <div className="flex items-center space-x-1">
                {navigationItems.map((item) => {
                  const Icon = item.icon;
                  const isActive = currentPage === item.id;

                  return (
                    <button
                      key={item.id}
                      onClick={() => handlePageChange(item.id)}
                      className={`flex items-center gap-2 px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                        isActive
                          ? 'bg-blue-100 text-blue-700'
                          : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                      }`}
                    >
                      <Icon className="w-4 h-4" />
                      <span>{item.name}</span>
                    </button>
                  );
                })}
              </div>
              <StatusIndicator />
            </div>
          </div>
        </div>
      </nav>

      {/* Mobile Navigation */}
      <nav className="md:hidden bg-white shadow-sm border-b">
        <div className="px-4 sm:px-6">
          <div className="flex justify-between items-center h-16">
            {/* Logo */}
            <div className="flex items-center gap-3">
              <Zap className="w-7 h-7 text-blue-600" />
              <div>
                <h1 className="text-lg font-bold text-gray-900">Motion Agent</h1>
                <p className="text-xs text-gray-600">Professional 3D Animation</p>
              </div>
            </div>

            {/* Mobile Menu Button */}
            <button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="p-2 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-100"
            >
              {isMobileMenuOpen ? (
                <X className="w-6 h-6" />
              ) : (
                <Menu className="w-6 h-6" />
              )}
            </button>
          </div>
        </div>

        {/* Mobile Menu */}
        {isMobileMenuOpen && (
          <div className="border-t bg-white">
            <div className="px-4 py-2 space-y-1">
              {navigationItems.map((item) => {
                const Icon = item.icon;
                const isActive = currentPage === item.id;
                
                return (
                  <button
                    key={item.id}
                    onClick={() => handlePageChange(item.id)}
                    className={`w-full flex items-center gap-3 px-3 py-3 rounded-lg text-left transition-colors ${
                      isActive
                        ? 'bg-blue-100 text-blue-700'
                        : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                    }`}
                  >
                    <Icon className="w-5 h-5" />
                    <div>
                      <div className="font-medium">{item.name}</div>
                      <div className="text-xs text-gray-500">{item.description}</div>
                    </div>
                  </button>
                );
              })}
            </div>
          </div>
        )}
      </nav>

      {/* Mobile Menu Overlay */}
      {isMobileMenuOpen && (
        <div
          className="md:hidden fixed inset-0 bg-black bg-opacity-25 z-40"
          onClick={() => setIsMobileMenuOpen(false)}
        />
      )}
    </>
  );
}
