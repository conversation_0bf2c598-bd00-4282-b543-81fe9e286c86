'use client';

import { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';
import { 
  Activity, 
  Server, 
  Database, 
  Brain, 
  Zap, 
  CheckCircle, 
  AlertCircle, 
  XCircle,
  RefreshCw,
  Monitor,
  Cpu,
  HardDrive,
  Network,
  Clock,
  TrendingUp,
  BarChart3
} from 'lucide-react';
import {
  HealthStatus,
  TaskStats
} from '@/types/motion';
import {
  checkHealth,
  checkAnimationHealth,
  getTaskStats
} from '@/lib/api';
import { handleApiError } from '@/lib/api';

interface StatusDashboardProps {
  autoRefresh?: boolean;
  refreshInterval?: number;
}

export default function StatusDashboard({ 
  autoRefresh = true, 
  refreshInterval = 30000 
}: StatusDashboardProps) {
  const [healthStatus, setHealthStatus] = useState<HealthStatus | null>(null);
  const [animationHealth, setAnimationHealth] = useState<any>(null);
  const [taskStats, setTaskStats] = useState<TaskStats | null>(null);
  const [loading, setLoading] = useState(false);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  useEffect(() => {
    loadAllStatus();
  }, []);

  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(() => {
      loadAllStatus();
    }, refreshInterval);

    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval]);

  const loadAllStatus = async () => {
    try {
      setLoading(true);
      
      // Load all status data in parallel
      const [health, animHealth, stats] = await Promise.allSettled([
        checkHealth(),
        checkAnimationHealth(),
        getTaskStats()
      ]);

      if (health.status === 'fulfilled') {
        setHealthStatus(health.value);
      }

      if (animHealth.status === 'fulfilled') {
        setAnimationHealth(animHealth.value);
      }

      if (stats.status === 'fulfilled') {
        setTaskStats(stats.value);
      }

      setLastUpdated(new Date());
    } catch (error) {
      console.error('Failed to load status:', error);
    } finally {
      setLoading(false);
    }
  };

  const getServiceStatus = (service: string) => {
    if (!healthStatus) return 'unknown';
    
    const services = healthStatus.features || {};
    if (service in services) {
      return services[service] ? 'healthy' : 'unhealthy';
    }
    return 'unknown';
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
      case 'ready':
      case 'running':
      case 'connected':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'unhealthy':
      case 'failed':
      case 'disconnected':
        return <XCircle className="w-5 h-5 text-red-500" />;
      case 'warning':
      case 'degraded':
        return <AlertCircle className="w-5 h-5 text-yellow-500" />;
      default:
        return <AlertCircle className="w-5 h-5 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy':
      case 'ready':
      case 'running':
      case 'connected':
        return 'text-green-600 bg-green-100';
      case 'unhealthy':
      case 'failed':
      case 'disconnected':
        return 'text-red-600 bg-red-100';
      case 'warning':
      case 'degraded':
        return 'text-yellow-600 bg-yellow-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const formatUptime = (seconds: number) => {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (days > 0) return `${days}d ${hours}h ${minutes}m`;
    if (hours > 0) return `${hours}h ${minutes}m`;
    return `${minutes}m`;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold flex items-center gap-2">
          <Monitor className="w-7 h-7" />
          System Status Dashboard
        </h2>
        <div className="flex items-center gap-4">
          {lastUpdated && (
            <div className="text-sm text-gray-500 flex items-center gap-1">
              <Clock className="w-4 h-4" />
              Last updated: {lastUpdated.toLocaleTimeString()}
            </div>
          )}
          <button
            onClick={loadAllStatus}
            disabled={loading}
            className="flex items-center gap-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
          >
            <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </button>
        </div>
      </div>

      {/* Overall System Status */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
          <Activity className="w-5 h-5" />
          Overall System Health
        </h3>
        
        {healthStatus ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="flex items-center justify-center mb-2">
                <CheckCircle className="w-8 h-8 text-green-600" />
              </div>
              <div className="font-semibold text-green-700">System Online</div>
              <div className="text-sm text-green-600">Version {healthStatus.version}</div>
            </div>
            
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="flex items-center justify-center mb-2">
                <Brain className="w-8 h-8 text-blue-600" />
              </div>
              <div className="font-semibold text-blue-700">NLU Pipeline</div>
              <div className={`text-sm px-2 py-1 rounded-full ${getStatusColor(healthStatus.nlu_pipeline || 'unknown')}`}>
                {healthStatus.nlu_pipeline || 'Unknown'}
              </div>
            </div>
            
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <div className="flex items-center justify-center mb-2">
                <Zap className="w-8 h-8 text-purple-600" />
              </div>
              <div className="font-semibold text-purple-700">LangGraph</div>
              <div className={`text-sm px-2 py-1 rounded-full ${getStatusColor(healthStatus.langgraph_pipeline || 'unknown')}`}>
                {healthStatus.langgraph_pipeline || 'Unknown'}
              </div>
            </div>
            
            <div className="text-center p-4 bg-orange-50 rounded-lg">
              <div className="flex items-center justify-center mb-2">
                <Server className="w-8 h-8 text-orange-600" />
              </div>
              <div className="font-semibold text-orange-700">Animator</div>
              <div className={`text-sm px-2 py-1 rounded-full ${getStatusColor(healthStatus.professional_animator || 'unknown')}`}>
                {healthStatus.professional_animator || 'Unknown'}
              </div>
            </div>
          </div>
        ) : (
          <div className="text-center py-8 text-gray-500">
            {loading ? 'Loading system status...' : 'Unable to load system status'}
          </div>
        )}
      </div>

      {/* Service Details */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Core Services */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
            <Server className="w-5 h-5" />
            Core Services
          </h3>
          
          <div className="space-y-3">
            {[
              { name: 'API Server', key: 'api_server', icon: Server },
              { name: 'Database', key: 'database', icon: Database },
              { name: 'Task Queue', key: 'task_queue', icon: Activity },
              { name: 'Animation Pipeline', key: 'animation_pipeline', icon: Zap },
            ].map((service) => (
              <div key={service.key} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-3">
                  <service.icon className="w-5 h-5 text-gray-600" />
                  <span className="font-medium">{service.name}</span>
                </div>
                <div className="flex items-center gap-2">
                  {getStatusIcon('healthy')}
                  <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor('healthy')}`}>
                    Healthy
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Task Statistics */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
            <BarChart3 className="w-5 h-5" />
            Task Statistics
          </h3>
          
          {taskStats ? (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-3 bg-blue-50 rounded-lg">
                  <div className="text-2xl font-bold text-blue-700">{taskStats.total_tasks}</div>
                  <div className="text-sm text-blue-600">Total Tasks</div>
                </div>
                <div className="text-center p-3 bg-green-50 rounded-lg">
                  <div className="text-2xl font-bold text-green-700">{taskStats.success_rate.toFixed(1)}%</div>
                  <div className="text-sm text-green-600">Success Rate</div>
                </div>
              </div>
              
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Running</span>
                  <span className="font-medium">{taskStats.running_tasks}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Pending</span>
                  <span className="font-medium">{taskStats.pending_tasks}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Completed</span>
                  <span className="font-medium">{taskStats.completed_tasks}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Failed</span>
                  <span className="font-medium">{taskStats.failed_tasks}</span>
                </div>
              </div>
              
              {taskStats.average_duration && (
                <div className="pt-3 border-t">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Avg Duration</span>
                    <span className="font-medium">{Math.round(taskStats.average_duration)}s</span>
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              {loading ? 'Loading task statistics...' : 'Unable to load task statistics'}
            </div>
          )}
        </div>
      </div>

      {/* Feature Status */}
      {healthStatus?.features && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
            <TrendingUp className="w-5 h-5" />
            Feature Status
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {Object.entries(healthStatus.features).map(([feature, enabled]) => (
              <div key={feature} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <span className="font-medium capitalize">
                  {feature.replace(/_/g, ' ')}
                </span>
                <div className="flex items-center gap-2">
                  {enabled ? (
                    <>
                      <CheckCircle className="w-4 h-4 text-green-500" />
                      <span className="text-xs text-green-600 bg-green-100 px-2 py-1 rounded-full">
                        Enabled
                      </span>
                    </>
                  ) : (
                    <>
                      <XCircle className="w-4 h-4 text-red-500" />
                      <span className="text-xs text-red-600 bg-red-100 px-2 py-1 rounded-full">
                        Disabled
                      </span>
                    </>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* System Resources (Mock Data) */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
          <Cpu className="w-5 h-5" />
          System Resources
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm text-gray-600">CPU Usage</span>
              <span className="font-medium">45%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div className="bg-blue-600 h-2 rounded-full" style={{ width: '45%' }}></div>
            </div>
          </div>
          
          <div>
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm text-gray-600">Memory Usage</span>
              <span className="font-medium">62%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div className="bg-green-600 h-2 rounded-full" style={{ width: '62%' }}></div>
            </div>
          </div>
          
          <div>
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm text-gray-600">Disk Usage</span>
              <span className="font-medium">28%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div className="bg-purple-600 h-2 rounded-full" style={{ width: '28%' }}></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
