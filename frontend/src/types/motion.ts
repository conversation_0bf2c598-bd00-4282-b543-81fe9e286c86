// Motion Agent TypeScript Types
export interface TextInput {
  text: string;
  character_id: string;
}

export interface AdvancedTextInput extends TextInput {
  use_langgraph: boolean;
  context?: Record<string, unknown>;
}

export interface AnimationRequest {
  text: string;
  character_id: string;
  quality_target: string;
  frame_rate: number;
  export_format: string;
  context?: Record<string, unknown>;
  reference_animations: string[];
}

export interface MotionResponse {
  success: boolean;
  message: string;
  action_sequence?: {
    actions: string[];
    duration: number;
    complexity?: string;
  };
  error_message?: string;
}

export interface AnimationResponse {
  success: boolean;
  animation_sequence?: {
    id: string;
    name: string;
    actions: unknown[];
    total_duration: number;
    frame_rate: number;
    total_frames: number;
    character_id: string;
  };
  fbx_file_path?: string;
  original_text: string;
  processed_actions: string[];
  quality_report: Record<string, unknown>;
  error_message?: string;
  warnings: string[];
  processing_time?: number;
}

export interface AnimationPresets {
  locomotion: string[];
  acrobatic: string[];
  combat: string[];
  gestures: string[];
  expressions: string[];
  idle: string[];
}

export interface ExampleRequest {
  text: string;
  description: string;
  animator_level: string;
}

export interface HealthStatus {
  status: string;
  nlu_pipeline?: string;
  langgraph_pipeline?: string;
  professional_animator?: string;
  version: string;
  features: Record<string, boolean>;
}

// Motion Generation Types
export interface MotionRequest {
  text: string;
  character_id?: string;
  context?: Record<string, any>;
}

export interface AdvancedMotionRequest extends MotionRequest {
  use_langgraph?: boolean;
}

// Conversation Types
export enum ConversationStatus {
  ACTIVE = 'active',
  PAUSED = 'paused',
  COMPLETED = 'completed',
  ARCHIVED = 'archived'
}

export interface ConversationThread {
  id: string;
  title: string;
  description?: string;
  status: ConversationStatus;
  user_id?: string;
  session_id?: string;
  character_id: string;
  context: Record<string, any>;
  settings: Record<string, any>;
  message_count: number;
  total_tokens: number;
  created_at: string;
  updated_at: string;
  last_activity_at: string;
  is_pinned: boolean;
}

export interface ConversationCreate {
  title: string;
  description?: string;
  character_id?: string;
  user_id?: string;
  session_id?: string;
  context?: Record<string, any>;
  settings?: Record<string, any>;
}

export interface ConversationUpdate {
  title?: string;
  description?: string;
  status?: ConversationStatus;
  character_id?: string;
  context?: Record<string, any>;
  settings?: Record<string, any>;
  is_pinned?: boolean;
}

export interface ConversationList {
  threads: ConversationThread[];
  total: number;
  page: number;
  page_size: number;
  has_next: boolean;
  has_prev: boolean;
}

export interface Message {
  id: string;
  conversation_id: string;
  content: string;
  message_type: 'user' | 'assistant' | 'system';
  user_id?: string;
  metadata: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface MessageCreate {
  content: string;
  message_type?: 'user' | 'assistant' | 'system';
  user_id?: string;
  metadata?: Record<string, any>;
}

export interface ConversationHistory {
  conversation_id: string;
  messages: Message[];
  total_messages: number;
  page: number;
  page_size: number;
  has_next: boolean;
  has_prev: boolean;
}

// Task Types
export enum TaskType {
  ANIMATION_GENERATION = 'animation_generation',
  NLU_PROCESSING = 'nlu_processing',
  MOTION_CAPTURE = 'motion_capture',
  BLENDER_EXPORT = 'blender_export',
  CONVERSATION_PROCESSING = 'conversation_processing',
  SYSTEM_MAINTENANCE = 'system_maintenance'
}

export enum TaskStatus {
  PENDING = 'pending',
  QUEUED = 'queued',
  RUNNING = 'running',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
  RETRYING = 'retrying'
}

export enum TaskPriority {
  LOW = 'low',
  NORMAL = 'normal',
  HIGH = 'high',
  URGENT = 'urgent'
}

export interface Task {
  id: string;
  task_id: string;
  task_type: TaskType;
  task_name: string;
  description?: string;
  priority: TaskPriority;
  status: TaskStatus;
  progress: number;
  conversation_id?: string;
  message_id?: string;
  user_id?: string;
  input_data: Record<string, any>;
  output_data: Record<string, any>;
  metadata: Record<string, any>;
  worker_id?: string;
  queue_name?: string;
  retry_count: number;
  max_retries: number;
  created_at: string;
  started_at?: string;
  completed_at?: string;
  estimated_duration?: number;
  actual_duration?: number;
  error_message?: string;
  error_code?: string;
  error_details?: Record<string, any>;
  is_cancelled: boolean;
  is_system_task: boolean;
}

export interface TaskCreate {
  task_type: TaskType;
  task_name: string;
  description?: string;
  priority?: TaskPriority;
  conversation_id?: string;
  message_id?: string;
  user_id?: string;
  input_data?: Record<string, any>;
  metadata?: Record<string, any>;
  queue_name?: string;
  max_retries?: number;
  estimated_duration?: number;
}

export interface TaskUpdate {
  status?: TaskStatus;
  progress?: number;
  output_data?: Record<string, any>;
  metadata?: Record<string, any>;
  worker_id?: string;
  started_at?: string;
  completed_at?: string;
  actual_duration?: number;
  error_message?: string;
  error_code?: string;
  error_details?: Record<string, any>;
}

export interface TaskList {
  tasks: Task[];
  total: number;
  page: number;
  page_size: number;
  has_next: boolean;
  has_prev: boolean;
}

export interface TaskStats {
  total_tasks: number;
  pending_tasks: number;
  running_tasks: number;
  completed_tasks: number;
  failed_tasks: number;
  cancelled_tasks: number;
  average_duration?: number;
  success_rate: number;
}
