# Professional Motion Agent with Taskiq

专业游戏动画师系统 - A Professional Game Animator System that converts natural language descriptions into high-quality 3D character animations using advanced NLU, professional animator functions, Blender integration, and asynchronous task processing.

## 🎯 核心特性 Core Features

- **🧠 智能NLU**: 使用spaCy + Transformers将自然语言转换为专业动画师术语
- **👨‍🎨 专业动画师**: 实现初级和中级动画师的核心职能
- **🎬 完整流程**: 从自然语言到FBX文件的端到端解决方案
- **⚡ 高性能**: 基于FastAPI + LangChain + LangGraph + Taskiq的现代架构
- **🔄 异步处理**: 基于Taskiq的任务队列系统，支持对话线程管理
- **💾 数据持久化**: PostgreSQL数据库存储对话历史和任务状态
- **🔴 缓存支持**: Redis缓存和任务队列后端

## 🏗️ 系统架构 System Architecture

```
motion-agent/
├── frontend/                          # 🌐 Next.js前端界面
│   ├── src/                          # 前端源代码
│   │   ├── app/                      # Next.js App Router
│   │   │   ├── page.tsx             # 主页面
│   │   │   ├── layout.tsx           # 根布局
│   │   │   └── globals.css          # 全局样式
│   │   ├── components/              # React组件
│   │   │   ├── MotionGenerator.tsx  # 动画生成器
│   │   │   ├── AnimationPreview.tsx # 动画预览
│   │   │   └── ExampleRequests.tsx  # 示例请求
│   │   ├── lib/                     # 工具库
│   │   │   └── api.ts              # API客户端
│   │   └── types/                   # TypeScript类型
│   │       └── motion.ts           # 动画相关类型
│   ├── public/                      # 静态资源
│   ├── package.json                 # 前端依赖配置
│   └── README.md                    # 前端文档
├── backend/                         # 🔧 后端代码
│   ├── src/                        # 核心源代码
│   │   ├── animation/              # 动画处理模块
│   │   │   ├── expression_animator.py
│   │   │   ├── motion_blender.py
│   │   │   └── motion_cleaner.py
│   │   ├── export/                 # 导出模块
│   │   │   ├── fbx_exporter.py
│   │   │   └── format_converter.py
│   │   ├── nlu/                    # 自然语言理解
│   │   │   ├── action_extractor.py
│   │   │   ├── pipeline.py
│   │   │   └── scene_parser.py
│   │   ├── scene/                  # 场景控制
│   │   │   ├── camera_controller.py
│   │   │   ├── environment_controller.py
│   │   │   └── sound_controller.py
│   │   └── main.py                 # 主程序入口
│   ├── tests/                      # 测试文件
│   │   ├── results/               # 测试结果
│   │   ├── test_basic_features.py
│   │   ├── test_enhanced_features.py
│   │   ├── test_fixed_features.py
│   │   ├── test_framework.py
│   │   └── demo_enhanced_features.py
│   ├── blender_scripts/           # Blender脚本
│   │   └── generate_animation.py
│   ├── scripts/                   # 数据库初始化脚本
│   │   ├── init-db.sql
│   │   └── init-mongodb.js
│   ├── logs/                      # 日志文件
│   ├── output/                    # 输出文件
│   │   └── animations/           # 生成的FBX文件
│   ├── temp/                      # 临时文件
│   │   └── animation_data/
│   ├── app.py                     # FastAPI主服务
│   ├── database.py                # 数据库配置
│   ├── models.py                  # 数据模型
│   ├── config.py                  # 配置管理
│   ├── start_backend.py           # 后端启动脚本
│   ├── run.py                     # 快速启动脚本
│   ├── requirements.txt           # 后端依赖
│   ├── ruff.toml                  # 代码检查配置
│   └── docker-compose.dev.yml     # 开发环境配置
├── pyproject.toml                 # 项目配置
├── uv.lock                        # 依赖锁定文件
├── start.py                       # 🚀 项目启动器
└── README.md                      # 项目说明文档
```

## 🎯 专业动画师职能 Professional Animator Functions

### 🎨 统一专业动画师 (Unified Professional Animator)
- **🧹 动捕清理**: 清理动作捕捉数据的噪点和错误
- **🚶 基础移动**: 走、跑、跳等基础移动动画
- **🔄 循环动画**: Idle、呼吸等循环动画制作
- **🔗 动作过渡**: 动作之间的智能过渡
- **⚔️ 战斗动画**: 攻击、受击、格挡等战斗动画
- **🤸 复杂动作**: 翻滚、后空翻720度等特技动作
- **🎭 动作融合**: 多个动作的平滑过渡和融合
- **😊 表情动画**: 面部表情和情绪表达

## 🛠️ 技术特性 Technical Features

- **🧠 智能NLU**: spaCy + Transformers + Haystack进行专业术语转换
- **🎬 专业动画**: 基于真实动画师工作流程的动画生成
- **🎨 Blender集成**: 专业级3D动画制作和FBX导出
- **⚡ 现代架构**: FastAPI + LangChain + LangGraph + Loguru
- **🔧 开发工具**: Ruff快速代码检查和格式化
- **📊 质量保证**: 动画质量评估和优化建议

## 🚀 快速开始 Quick Start

### 一键启动项目

```bash
# 1. 克隆仓库
git clone <repository-url>
cd motion-agent

# 2. 安装依赖
uv sync

# 3. 使用项目启动器
python start.py

# 或者手动启动各个服务:

# 启动后端服务
python backend/start_backend.py

# 启动前端服务 (在新终端中)
cd frontend
pnpm dev

# 运行测试
python backend/tests/test_basic_features.py
```

### 服务访问地址

- **前端界面**: http://localhost:3000
- **API服务**: http://localhost:9000
- **API文档**: http://localhost:9000/docs
- **健康检查**: http://localhost:9000/health

## Installation

### Prerequisites

- Python 3.11+
- Docker & Docker Compose (for development environment)
- Blender 3.0+ (for animation generation)
- uv (recommended) or Poetry

### Using uv (Recommended)

```bash
# Clone the repository
git clone <repository-url>
cd motion-agent

# Install dependencies
uv sync

# Copy environment configuration
cp .env.example .env
```

### Using Poetry

```bash
# Clone the repository
git clone <repository-url>
cd motion-agent

# Install dependencies
poetry install

# Activate virtual environment
poetry shell
```

## Usage

### Starting the API Server

```bash
# Using Poetry
poetry run uvicorn backend.app:app --reload --host 0.0.0.0 --port 9000

# Using Python directly
cd backend
python app.py
```

The API will be available at `http://localhost:9000`

### API Documentation

Once the server is running, visit:
- Swagger UI: `http://localhost:9000/docs`
- ReDoc: `http://localhost:9000/redoc`

### 🎬 专业动画师API使用示例

```bash
# 生成复杂动作序列 - 后空翻720度 + 移动 + 转身
curl -X POST "http://localhost:9000/animation/generate" \
     -H "Content-Type: application/json" \
     -d '{
       "text": "生成一个后空翻720度之后，向前五步走，然后转身，并向左迈一步",
       "character_id": "hero_character",
       "quality_target": "game_ready",
       "frame_rate": 30,
       "export_format": "fbx"
     }'

# 生成战斗动作
curl -X POST "http://localhost:9000/animation/generate" \
     -H "Content-Type: application/json" \
     -d '{
       "text": "快速冲刺攻击，然后防御姿态",
       "character_id": "warrior"
     }'

# 生成基础移动动画
curl -X POST "http://localhost:9000/animation/generate" \
     -H "Content-Type: application/json" \
     -d '{
       "text": "慢慢走向前方，然后挥手打招呼",
       "character_id": "npc_01"
     }'
```

### Using Blender Script

```bash
# Run Blender script with motion data
blender --background --python blender_scripts/generate_animation.py -- \
        --input motion_data.json \
        --output animation.fbx \
        --format fbx
```

## Supported Actions

### Locomotion
- walk, run, jump, hop, skip, march, stride

### Poses
- sit, stand, crouch, kneel, lie, lean, bend

### Gestures
- wave, point, clap, nod, shake, bow, salute

### Interactions
- pick, grab, hold, carry, push, pull, throw, catch

### Expressions
- smile, frown, blink

## Configuration

### Environment Variables

Create a `.env` file in the project root:

```env
# API Configuration
API_HOST=0.0.0.0
API_PORT=9000
DEBUG=true

# Blender Configuration
BLENDER_PATH=/path/to/blender
DEFAULT_CHARACTER_MODEL=/path/to/character.blend

# Animation Settings
DEFAULT_FRAME_RATE=24
DEFAULT_ANIMATION_QUALITY=medium

# Logging Configuration
LOG_LEVEL=INFO
LOG_ROTATION=10 MB
LOG_RETENTION=7 days
```

### Logging

The application uses Loguru for structured logging with the following features:

- **Console Output**: Colored, formatted logs for development
- **File Logging**: Automatic log rotation and retention in `logs/motion_agent.log`
- **Log Levels**: DEBUG, INFO, WARNING, ERROR, SUCCESS
- **Structured Format**: Includes timestamp, level, module, function, and line number

Log files are automatically rotated when they reach 10MB and kept for 7 days by default.

### LangChain & LangGraph Integration

The project includes two NLU pipelines:

#### Basic NLU Pipeline (`/generate-motion`)
- Simple keyword-based action extraction
- Fast processing for basic motion commands
- Suitable for simple animations

#### Advanced LangGraph Pipeline (`/generate-motion-advanced`)
- **Structured Workflows**: Uses LangGraph for complex processing chains
- **State Management**: Maintains context throughout the processing pipeline
- **Validation**: Built-in action sequence validation
- **Error Handling**: Sophisticated error recovery and reporting
- **Extensible**: Easy to add new processing nodes

**LangGraph Workflow Steps:**
1. **Preprocess**: Text normalization and cleaning
2. **Extract Actions**: Advanced action extraction with context
3. **Validate Sequence**: Check for physical possibility and conflicts
4. **Create Sequence**: Generate final action sequence
5. **Error Handling**: Graceful error recovery

## Development

### Code Formatting and Linting

```bash
# Format code with Black
poetry run black backend/

# Lint and format with Ruff (faster alternative)
poetry run ruff check backend/
poetry run ruff format backend/

# Fix issues automatically with Ruff
poetry run ruff check --fix backend/

# Lint with flake8 (legacy)
poetry run flake8 backend/

# Type checking with mypy
poetry run mypy backend/
```

### Ruff Configuration

Ruff is configured for:
- **Fast linting**: Much faster than flake8
- **Auto-fixing**: Automatically fix many issues
- **Black compatibility**: Same formatting rules as Black
- **Comprehensive rules**: Includes pycodestyle, Pyflakes, isort, and more

Configuration is in `pyproject.toml` and `.ruff.toml`.

### Pre-commit Hooks

```bash
# Install pre-commit hooks
poetry run pre-commit install

# Run hooks manually
poetry run pre-commit run --all-files
```

## 🌐 API Endpoints

### 对话管理 Conversation Management

#### POST /conversations/
创建新对话线程

```json
{
  "title": "动画生成对话",
  "description": "创建角色行走动画",
  "character_id": "default",
  "context": {"scene": "outdoor"}
}
```

#### GET /conversations/
获取对话列表，支持过滤和分页

#### GET /conversations/{id}
获取对话详情

#### POST /conversations/{id}/messages
发送消息到对话

```json
{
  "content": "请创建一个角色向前走5步的动画",
  "message_type": "user"
}
```

#### POST /conversations/{id}/animation
开始动画生成

```json
{
  "text": "角色向前走5步",
  "character_id": "default",
  "context": {"style": "realistic"}
}
```

### 任务管理 Task Management

#### GET /tasks/
获取任务列表，支持过滤和分页

#### GET /tasks/{id}
获取任务详情和状态

#### POST /tasks/{id}/cancel
取消任务

#### GET /tasks/stats/overview
获取任务统计信息

### 传统API Endpoints (兼容性)

#### POST /generate-motion
使用基础NLU管道生成动画

```json
{
  "text": "Walk forward slowly and then wave your right hand",
  "character_id": "character_01"
}
```

#### POST /generate-motion-advanced
使用LangGraph管道生成动画

```json
{
  "text": "Walk forward slowly and then wave your right hand",
  "character_id": "character_01",
  "use_langgraph": true,
  "context": {
    "environment": "indoor",
    "mood": "happy"
  }
}
```

**Response:**
```json
{
  "success": true,
  "action_sequence": {
    "id": "seq_123",
    "actions": [
      {
        "id": "act_1",
        "type": "locomotion",
        "name": "walk",
        "duration": 3.0,
        "start_time": 0.0,
        "parameters": {
          "speed": "slow",
          "direction": "forward"
        }
      }
    ],
    "total_duration": 3.0,
    "character_id": "character_01"
  },
  "blender_script_path": "blender_scripts/generate_animation.py"
}
```

### GET /health

Health check endpoint.

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- FastAPI for the excellent web framework
- Blender for the powerful 3D animation capabilities
- NLTK for natural language processing tools
