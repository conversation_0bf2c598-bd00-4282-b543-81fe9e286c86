#!/usr/bin/env python3
"""
Motion Agent Project Launcher
Motion Agent 项目启动器
"""

import subprocess
import sys
import os
from pathlib import Path



def start_backend():
    """启动后端服务"""
    print("\n🚀 启动后端服务...")
    backend_script = Path("backend/start_backend.py")

    if not backend_script.exists():
        print(f"❌ 后端启动脚本不存在: {backend_script}")
        sys.exit(1)
    try:
        subprocess.run([sys.executable, str(backend_script)])
    except Exception as e:
        print(f"💥 启动后端失败: {e}")


if __name__ == "__main__":
    start_backend()
